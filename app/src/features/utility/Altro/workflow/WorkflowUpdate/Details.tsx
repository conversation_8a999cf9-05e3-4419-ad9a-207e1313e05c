import { useTranslation } from "@1f/react-sdk";
import {
    Autocomplete,
    Button,
    NotificationInline,
    Stack,
    TextField,
} from "@vapor/react-material";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useEffect, useState } from "react";
import ConfirmModal from "../../../../../custom-components/ConfirmModal";
import { useStore } from "./Rete/Rete";
import { useSaveEditorData } from "./hooks/useSaveEditorData";
import { useUpdateTags } from "./hooks/useUpdateTags";
import usePostCustom from "../../../../../hooks/usePostCustom";
import useGetCustom from "../../../../../hooks/useGetCustom";
import { debounce } from "lodash";

export const Details = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const uniqueid = searchParams.get("uniqueid");
    const [confirmDuplication, setConfirmDuplication] = useState(false);
    const [name, setName] = useState(t("Nome non definito"));
    const [description, setDescription] = useState("");

    const [newTag, setNewTag] = useState("");
    const [tags, setTags] = useState<string[]>([]);

    const { editorData, saveState, setSaveState } = useStore();

    const tagsRequest = useGetCustom(
        "/archive/getavailabletags?noTemplateVars=true"
    );

    useEffect(() => {
        if (!newTag) return;

        const debouncedFetch = debounce(() => {
            tagsRequest.doFetch(true, { searchWord: newTag });
        }, 500);

        debouncedFetch();

        return () => {
            debouncedFetch.cancel();
        };
    }, [newTag]);

    useUpdateTags({
        uniqueid: uniqueid,
        fileTags: tags,
        save: saveState,
    });

    const detailsRequest = useGetCustom(
        "/workflows/update?noTemaplateVars=true"
    );

    useEffect(() => {
        if (uniqueid) {
            detailsRequest.doFetch(true, { uniqueid: uniqueid });
        }
    }, [uniqueid]);

    useEffect(() => {
        if (detailsRequest.hasLoaded) {
            setName(detailsRequest.data.nome);
            setDescription(detailsRequest.data.descrizione);
            setTags(
                JSON.parse(detailsRequest.data.tags).map(
                    (tag: any) => tag.description
                )
            );
        }
    }, [detailsRequest.hasLoaded]);

    const saveResponse = useSaveEditorData({
        descrizione: description,
        flowJSON: editorData,
        nome: name,
        save: saveState,
        uniqueid: uniqueid,
    });

    const duplicateRequest = usePostCustom(
        "workflows/dupe?noTemplateVars=true"
    );

    const duplicate = () => {
        if (uniqueid) {
            duplicateRequest.doFetch(true, { uniqueid: uniqueid });
        }
    };

    useEffect(() => {
        if (duplicateRequest.hasLoaded || saveResponse.hasLoaded) {
            navigate("/workflows");
            setSaveState(false);
        }
    }, [duplicateRequest.hasLoaded, saveResponse.hasLoaded]);

    const infoText = t(
        "NOTA: I tag vanno inseriti parola per parola (usare lo spazio o l'invio per separarli), si può utilizzare l'underscore (trattino basso) per espressioni composte come ad esempio \"decreto_ingiuntivo\"."
    );
    const confirmText = t(
        "Assicurarsi di aver salvato tutte le modifiche dell'attuale workflow, proseguire?"
    );

    return (
        <Stack gap={2}>
            <ConfirmModal
                agree={t("Si")}
                decline={t("Annulla")}
                handleAgree={() => {
                    duplicate();
                }}
                handleDecline={() => {
                    setConfirmDuplication(false);
                }}
                title={t("Duplica Modello?")}
                colorConfirmButton="success"
                confirmText={confirmText}
                open={confirmDuplication}
            />
            <Stack
                direction="row"
                justifyContent="space-between">
                <NotificationInline
                    variant="outlined"
                    severity="info"
                    color="info"
                    sx={{ width: 600 }}>
                    {infoText}
                </NotificationInline>
                <Button
                    variant="contained"
                    onClick={() => setConfirmDuplication(true)}>
                    {t("Duplica Modello")}
                </Button>
            </Stack>
            <Stack
                sx={{ width: 400 }}
                gap={2}>
                <TextField
                    value={name}
                    onChange={e => setName(e.target.value)}
                    label="Nome"
                />
                <TextField
                    value={description}
                    onChange={e => setDescription(e.target.value)}
                    label="Descrizione"
                />
                <Autocomplete
                    freeSolo
                    multiple
                    selectOnFocus
                    handleHomeEndKeys
                    filterOptions={x => x}
                    value={tags}
                    onChange={(_e, value: string[]) => setTags(value)}
                    options={
                        tagsRequest.hasLoaded
                            ? tagsRequest.data.map(
                                  (tag: any) => tag.description
                              )
                            : []
                    }
                    inputValue={newTag}
                    loading={tagsRequest.loading}
                    onInputChange={(_e, newValue) => setNewTag(newValue)}
                    renderInput={params => (
                        <TextField
                            {...params}
                            sx={{ width: 400 }}
                            label={t("Tags")}
                        />
                    )}
                />
            </Stack>
        </Stack>
    );
};
