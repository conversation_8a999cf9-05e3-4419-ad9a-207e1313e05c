import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useDeleteLoan = ({
    uniqueid,
    remove,
}: {
    uniqueid: string | undefined | null;
    remove: boolean;
}) => {
    const { loading, doFetch, hasLoaded } = usePostCustom("/loan/delete");

    useEffect(() => {
        if (uniqueid && remove) {
            doFetch(true, { uniqueid: uniqueid });
        }
    });
    return { loading, hasLoaded };
};
