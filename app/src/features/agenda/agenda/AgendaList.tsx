import VaporPage from "@vapor/react-custom/VaporPage";
import { useRef, useState, useEffect } from "react";
import PageTitle from "../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { useAgendaHooks } from "./hooks/useAgendaHooks";
import useGetCustom from "../../../hooks/useGetCustom";
import { DEFAULT_LIST_PARAMS } from "./Index";
import { Filters } from "./sections/Filters";
import { useLocation, useNavigate } from "react-router-dom";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import PrintIcon from "@mui/icons-material/Print";
import StampaTemplateModal from "./sections/StampaTemplateModal";
import { updateParams } from "./utils";
import { debounce } from "lodash";
import ToastNotification from "../../../custom-components/ToastNotification";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import {
  GridPaginationModel,
  GridCallbackDetails,
  GridRowSelectionModel,
} from "@mui/x-data-grid-pro";

export default function AgendaIndex() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();



  const [openModalStampaTemplate, setOpenModalStampaTemplate] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const [totalRows, setTotalRows] = useState<number>(0);
  const [defaultParams, setDefaultParams] = useState(
    location.state?.defaultParams
      ? location.state.defaultParams
      : DEFAULT_LIST_PARAMS
  );
  const [showErrorMessage, setShowErrorMessage] = useState(false);

  const [date, setDate] = useState({
    startDateSearch: new Date(),
    endDateSearch: null,
  });

  const {
    columns,
    searchCities,
    searchInstructors,
    searchAuthorities,
    searchLawyers,
    searchReferents,
    evase,
    items,
  } = useAgendaHooks();

  const resetClicked = useRef(false);

  const agendaListRequest = useGetCustom(
    "agenda/list",
    updateParams(defaultParams, date)
  );

  const exportCsvRequest = useGetCustom(
    "agenda/exportcsv",
    updateParams(defaultParams, date)
  );

  const printBySearchRequest = useGetCustom(
    "agenda/printbysearch",
    updateParams(defaultParams, date),
    null,
    true
  );

  const defaultagendaListRequest = useGetCustom(
    "agenda/list",
    DEFAULT_LIST_PARAMS
  );

  const onPageChange = (_: any, page: number) => {
    setDefaultParams({
      ...defaultParams,
      page,
    });
  };

  const onDateChange = (name: string, value: Date) => {
    setDate((prevValue: any) => ({ ...prevValue, [name]: value }));
    setDefaultParams({
      ...defaultParams,
      [name]: value,
    });
  };

  const onSortChange = (column: any, direction: any) => {
    setDefaultParams({
      ...defaultParams,
      sortColumn: column,
      sortOrder: direction,
    });
  };

  const startSearchList = async (reset = false) => {
    const response: any = await (reset
      ? defaultagendaListRequest.doFetch(true)
      : agendaListRequest.doFetch(true));

    if (reset) {
      resetClicked.current = false;
    }

    const { currentPage, totalRows } = response.data;

    setData(currentPage);
    setTotalRows(totalRows);
  };

  const onClickCallback = (uniqueid: any) => {
    const row = data.find((item: any) => item.uniqueid === uniqueid.toString());

    navigate(`/agenda/agenda/update/${uniqueid}`, {
      state: {
        origin: "agenda",
        type: "update",
        uniqueId: uniqueid,
        items: items,
        row,
        defaultParams,
        prevPath: location.pathname,
        rowDataUrl: 'agenda'
      },
    });
  };

  const onClickNewUdienza = () => {
    navigate(`/archiveagenda/agenda/create/new`, {
      state: {
        origin: "agenda",
        type: "create",
        items: items,
        prevPath: location.pathname,
        defaultParams,
        rowDataUrl: 'archiveagenda'
      },
    });
  };


  useEffect(() => {
    if (location.state && location.state.defaultParams) {
      setDefaultParams(location.state.defaultParams);
      const { startDateSearch, endDateSearch } = location.state.defaultParams;
      setDate({ startDateSearch, endDateSearch });
    }
  }, []);

  useEffect(() => {
    const debouncedSearch = debounce(() => {
      startSearchList();
    }, 500);
    defaultParams.searchActivity && debouncedSearch();
    return () => {
      debouncedSearch.cancel();
    };
  }, [defaultParams.searchActivity]);

  useEffect(() => {
    if (!resetClicked.current) {
      startSearchList();
    }
  }, [
    defaultParams.page,
    defaultParams.pageSize,
    defaultParams.sortColumn,
    defaultParams.sortOrder,
    defaultParams.searchCity,
    defaultParams.searchEvase,
    defaultParams.searchInstructors,
    defaultParams.searchAuthorities,
    defaultParams.searchReferents,
    defaultParams.ntbprocessed,
    defaultParams.isPostponed,
    defaultParams.searchLawyers,
    defaultParams.poliswebFilter,
    date,
  ]);

  const onPageChangeCallback = (model: GridPaginationModel) => {
    setDefaultParams({
      ...defaultParams,
      page: model.page,
      pageSize: model.pageSize,
    });
  };

  const handleRowSelection = (
    rowSelectionModel: GridRowSelectionModel,
    details: GridCallbackDetails<any>
  ) => {
    console.log("details", details);
    console.log("rowSelectionModel", rowSelectionModel);
  };

  const renderDataTable = () => {
    return (
      <CustomDataGrid
        name="agenda"
        columns={columns}
        data={data}
        page={defaultParams.page}
        totalRows={totalRows}
        pageSize={defaultParams.pageSize}
        loading={agendaListRequest.loading || defaultagendaListRequest.loading}
        query={defaultParams}
        setQuery={setDefaultParams}
        onPageChangeCallback={onPageChangeCallback}
        onClickCallback={onClickCallback}
        onRowSelectionModelChange={handleRowSelection}
        onClickKey="uniqueid"
      />
    );
  };

  const onChangeInput = (e: any) => {
    const { name, value } = e.target;

    const newParams = {
      ...defaultParams,
      [name]: value,
    };

    if (name === "searchEvase") {
      newParams.ntbprocessed = "0";
    }

    setDefaultParams(newParams);
  };

  const onChangeCheckbox = (e: any) => {
    const { name, checked } = e.target;
    setDefaultParams({
      ...defaultParams,
      [name]: checked || "",
    });
  };

  const updateDate = async () => {
    setDate((prevDate: any) => ({
      ...prevDate,
      startDateSearch: new Date(),
      endDateSearch: null,
    }));
  };

  const onClickReset = async () => {
    resetClicked.current = true;
    startSearchList(true);
    await updateDate();
    setDefaultParams(DEFAULT_LIST_PARAMS);
  };

  const handleSearchActivita = (event: any) => {
    if (event.key === "Enter") {
      startSearchList();
    }
  };

  const onChangeFunctions = {
    onChangeCheckbox,
    onChangeInput,
    onDateChange,
    onClickReset,
    onSortChange,
    onPageChange,
    handleSearchActivita,
  };

  const exportInCsv = async () => {
    const response: any = await exportCsvRequest.doFetch(true);
    const blob = new Blob([response.data], { type: "text/csv" });
    const link = document.createElement("a");
    link.href = window.URL.createObjectURL(blob);
    link.download = "Agenda_legale.csv";
    document.body.appendChild(link);
    link.click();
    if (link.parentNode) link.parentNode.removeChild(link);
  };

  const printBySearch = async () => {
    if (totalRows > 0) {
      const response: any = await printBySearchRequest.doFetch(
        updateParams(defaultParams, date, true)
      );
      const blob = new Blob([response.data], { type: "application/pdf" });
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = "Udienze.pdf";
      document.body.appendChild(link);
      link.click();

      if (link.parentNode) link.parentNode.removeChild(link);
    } else {
      setShowErrorMessage(true);
    }
  };

  const stampaTemplate = () => {
    setOpenModalStampaTemplate(!openModalStampaTemplate);
  };

  return (
    <VaporPage>
      <ToastNotification
        showNotification={showErrorMessage}
        setShowNotification={setShowErrorMessage}
        severity="error"
        text={t("Non è presente nessuna udienza da stampare")}
      />
      <StampaTemplateModal
        openModal={openModalStampaTemplate}
        setOpenModal={setOpenModalStampaTemplate}
        params={defaultParams}
        date={date}
      />
      <PageTitle
        title={t("AGENDA LEGALE")}
        showBackButton={false}
        actionButtons={[
          { label: t("Esporta in csv"), onclick: exportInCsv },
          {
            label: t("Stampa"),
            onclick: () => exportInCsv(),
            dropdown: true,
            options: [
              { label: t("Stampa"), handleClick: printBySearch },
              { label: t("Stampa Template"), handleClick: stampaTemplate },
            ],
            startIcon: <PrintIcon />,
          },
          {
            label: t(" Nuova udienza"),
            onclick: () => onClickNewUdienza(),
            variant: "contained",
            startIcon: <AddCircleOutlineIcon />,
          },
        ]}
      ></PageTitle>

      <VaporPage.Section>
        <Filters
          params={defaultParams}
          onChangeFunctions={onChangeFunctions}
          date={date}
          searchCities={searchCities}
          searchInstructors={searchInstructors}
          searchAuthorities={searchAuthorities}
          searchLawyers={searchLawyers}
          searchReferents={searchReferents}
          evase={evase}
        />
      </VaporPage.Section>

      <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
    </VaporPage>
  );
}
