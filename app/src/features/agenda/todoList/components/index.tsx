import { useState } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../../custom-components/PageTitle";
import useGetDeadlinesData from "../hooks/useGetDeadlinesData.ts";
import useListDeadlines from "../hooks/useListDeadlines.ts";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import Filters from "./filters.tsx";
import Spinner from "../../../../custom-components/Spinner";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import {
    GridPaginationModel,
    GridRowSelectionModel,
} from "@mui/x-data-grid-pro";
import { useNavigate } from "react-router-dom";
import ModifySubjectModal from "./modify.tsx";
import useUpdateDeadlineObj from "../hooks/useUpdateDeadlineObj.ts";
import usePrintImpegno from "../hooks/usePrintImpegno.ts";
import { useTranslation } from "@1f/react-sdk";

export default function ToDoListIndex() {
    const navigate = useNavigate();
    const { t } = useTranslation();
    const { deadlineData } = useGetDeadlinesData();
    const {
        deadlineQuery,
        setDeadlineQuery,
        list,
        loading,
        INITIAL_QUERY,
        fetchData,
    } = useListDeadlines();
    const {
        updateDeadlineParams,
        setUpdateDeadlineParams,
        handleUpdateDeadlineObject,
        disableModifyButton,
        setDisableModifyButton
    } = useUpdateDeadlineObj();
    const { handlePrintRequest, handleExportCSVRequest } = usePrintImpegno();
    const [openModal, setOpenModal] = useState<boolean>(false);

    const handleDialog = () => {
        setOpenModal(!openModal);
    };

    const handleNewImpegnoNavigation = () => {
        navigate("/impegno/update", { state: { isFromToDoList: true } });
    };

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setDeadlineQuery({
            ...deadlineQuery,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const onClickCallback = (uniqueid: string) => {
        navigate(`/impegno/update/${uniqueid}`, {
            state: { isFromToDoList: true },
        });
    };

    const handleRowSelection = (rowSelectionModel: GridRowSelectionModel) => {
        setUpdateDeadlineParams({
            ...updateDeadlineParams,
            deadline_uid: rowSelectionModel as string[],
        });
        if (rowSelectionModel.length === 0) {
            setDisableModifyButton(true);
        } else {
            setDisableModifyButton(false);
        }
    };

    console.log('list.columns', list.columns);

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="deadline"
                columns={list.columns}
                data={list.rows}
                page={list.page || 0}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                selectableRows={true}
                onPageChangeCallback={onPageChangeCallback}
                onClickCallback={onClickCallback}
                onRowSelectionModelChange={handleRowSelection}
                query={deadlineQuery}
                setQuery={setDeadlineQuery}
                onClickKey="uniqueid"
                onClickCheckboxKey="uniqueid"
            />
        );
    };

    return (
        <>
            <VaporPage>
                <PageTitle
                    title={t("Gestione Impegni")}
                    showBackButton={false}
                    actionButtons={[
                        {
                            label: t("Esporta in csv"),
                            onclick: () => handleExportCSVRequest(deadlineQuery),
                            variant: "outlined",
                        },
                        {
                            label: t("Stampa"),
                            onclick: () => handlePrintRequest(deadlineQuery),
                            variant: "outlined",
                        },
                        {
                            label: t("Modifica"),
                            onclick: () => setOpenModal(true),
                            disabled: disableModifyButton,
                            variant: "contained",
                        },
                        {
                            label: t("Nuovo Impegno"),
                            onclick: () => handleNewImpegnoNavigation(),
                            variant: "contained",
                            startIcon: <AddCircleOutlineIcon />,
                        },
                    ]}
                />

                <VaporPage.Section>
                    <Filters
                        deadlinePeople={deadlineData?.people}
                        deadlineTypes={deadlineData?.deadlineTypes}
                        deadlineCategories={deadlineData?.deadlineCategories}
                        deadlineQuery={deadlineQuery}
                        setDeadlineQuery={setDeadlineQuery}
                        INITIAL_QUERY={INITIAL_QUERY}
                    />
                </VaporPage.Section>

                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
            <ModifySubjectModal
                open={openModal}
                handleDialog={handleDialog}
                updateDeadlineParams={updateDeadlineParams}
                setUpdateDeadlineParams={setUpdateDeadlineParams}
                handleUpdateDeadlineObject={handleUpdateDeadlineObject}
                fetchData={fetchData}
            />
        </>
    );
}
