import { useState } from "react";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { IDeadlineParams } from "../interfaces/impegno.interface";

export default function useSaveDeadlines(postUrl?: string) {
    const saveDeadlineRequest = usePostCustom(
        `${postUrl ? postUrl : 'deadlines'}/save?noTemplateVars=true`
    );

    const overlappingDeadlinesRequest = usePostCustom(
        "deadlines/overlapping-deadlines?noTemplateVars=true"
    );

    const [requiredFields, setRequiredFields] = useState({
        deadlineText: false,
        deadlinePeriod: false,
        deadlineUser: false,
    });

    const saveDeadline = async (
        deadlineSaveParams: any,
        iscreatedFromDeadline: boolean = false,
        controlOverlapping: boolean = true
    ) => {

        if (deadlineSaveParams.deadlineText === "") {
            setRequiredFields({
                ...requiredFields,
                deadlineText: true,
            });
            return;
        }

        if (deadlineSaveParams.deadlinePeriod === "0" && iscreatedFromDeadline) {
            setRequiredFields({
                ...requiredFields,
                deadlinePeriod: true,
            });
            return;
        }

        if (!deadlineSaveParams.deadlineUser ||
            (Array.isArray(deadlineSaveParams.deadlineUser) && deadlineSaveParams.deadlineUser.length === 0)) {
            setRequiredFields({
                ...requiredFields,
                deadlineUser: true,
            });
            return;
        }

        const formData = new FormData();

        const deadlineUser : any = [];
        Object.keys(deadlineSaveParams).forEach((key) => {
            const value = deadlineSaveParams[key as keyof IDeadlineParams];

            if (value === false) return;

            if (key === "deadlineUser" && Array.isArray(value)) {
                value.forEach((user: any) => {
                    formData.append("deadlineUser[]", user.id);
                    deadlineUser.push(user.id);
                });
                const userIds = value.map((user: any) => user.id).join(",");
                formData.append("deadlineUserData", userIds);
                return;
            }

            if (key === "deadLinesGroups" && value?.id) {
                formData.append("deadLinesGroups", value.id);
                return;
            }

            formData.append(key, value);
        });

        if (controlOverlapping) {
            const responseOverlapping: any = await overlappingDeadlinesRequest.doFetch(true, { ...deadlineSaveParams, deadlineUser }, "post", "json", true);

            if (responseOverlapping.data.length > 0) {
                return {
                    overlappingDeadlines: true,
                    data: responseOverlapping.data
                };
            }
        }

        formData.delete('deadlineLinkud');

        const response: any = await saveDeadlineRequest.doFetch(true, formData);
        return response.data;
    };

    return {
        saveDeadline,
        requiredFields,
        setRequiredFields,
    };
}
