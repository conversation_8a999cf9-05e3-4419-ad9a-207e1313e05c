import { Button, <PERSON>u, MenuItem } from "@vapor/react-material";
import ArrowDropUpIcon from "@mui/icons-material/ArrowDropUp";
import { useState } from "react";

interface IButtonTimesheetMenu {
    handleCreateFromDeadline: (connect: boolean) => void;
    t: any;
}

export default function ButtonTimesheetMenu(props: IButtonTimesheetMenu) {
    const { handleCreateFromDeadline, t } = props;
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    return (
        <div>
            <Button
                aria-haspopup="true"
                variant="contained"
                endIcon={<ArrowDropUpIcon />}
                onClick={handleClick}
                aria-controls={open ? "basic-menu" : undefined}
                aria-expanded={open ? "true" : undefined}
            >
                {t("Crea Timesheet")}
            </Button>
            <Menu
                MenuListProps={{
                    "aria-labelledby": "basic-button",
                }}
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                transformOrigin={{
                    vertical: "bottom",
                    horizontal: "center",
                }}
                anchorOrigin={{
                    vertical: "top",
                    horizontal: "center",
                }}
            >
                <MenuItem onClick={() => handleCreateFromDeadline(false)}>
                    {t("Crea")}
                </MenuItem>
                <MenuItem onClick={() => handleCreateFromDeadline(true)}>
                    {t("Crea e collega")}
                </MenuItem>
            </Menu>
        </div>
    );
}
