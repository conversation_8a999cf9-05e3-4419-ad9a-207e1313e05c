import Spinner from "../../../../../../custom-components/Spinner";
import { Box, Button } from "@vapor/react-material";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import { useNavigate } from "react-router-dom";
import { CustomDataGrid } from "../../../../../../custom-components/CustomDataGrid";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";
// import useFetchDocuments from "../hooks/useFetchDocuments";
import { useTranslation } from "@1f/react-sdk";

interface IProps {
    documentsQuery: any;
    setDocumentsQuery: any;
    listDocuments: any;
    loadingDocuments: any;
    LINK_DOCUMENT: any;
}

export default function Documenti(props: IProps) {
    const {
        documentsQuery,
        setDocumentsQuery,
        listDocuments,
        loadingDocuments,
        LINK_DOCUMENT,
    } = props;
    const { t } = useTranslation();
    const navigate = useNavigate();

    const PATH_TO_DOCUMENTS = `/legacy${LINK_DOCUMENT.notinfile_add_url}?select_document&callback_function=${LINK_DOCUMENT.callback_function}&obj_uid=${LINK_DOCUMENT.obj_uid}`;

    const onPageChange = (
        model: GridPaginationModel,
        _: GridCallbackDetails<any>
    ) => {
        setDocumentsQuery({
            ...documentsQuery,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const renderDataTable = () => {
        if (loadingDocuments) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="impegniDocuments"
                columns={listDocuments.columns}
                data={listDocuments.rows || []}
                page={listDocuments.page}
                totalRows={listDocuments.totalRows}
                pageSize={listDocuments.pageSize}
                query={documentsQuery}
                setQuery={setDocumentsQuery}
                onPageChangeCallback={onPageChange}
            />
        );
    };

    return (
        <Box>
            <Box sx={{ display: "flex", justifyContent: "end" }}>
                <Button
                    startIcon={<AddCircleOutlineIcon />}
                    variant="outlined"
                    onClick={() => navigate(PATH_TO_DOCUMENTS)}
                >
                    {t("Collega")}
                </Button>
            </Box>
            <Box sx={{ ml: 2 }}>{renderDataTable()}</Box>
        </Box>
    );
}
