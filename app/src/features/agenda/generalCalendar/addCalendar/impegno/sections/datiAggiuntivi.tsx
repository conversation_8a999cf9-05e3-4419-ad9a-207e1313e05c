import React from "react";
import {
    Box,
    Typography,
    FormControl,
    Select,
    MenuItem,
    Button,
    Divider,
} from "@vapor/react-material";
import Spinner from "../../../../../../custom-components/Spinner";
import { useCalendarData } from "../context/CalendarDataContext";
import { useTranslation } from "@1f/react-sdk";

interface IProps {
    fetchDatiAggiuntivi: (selectedId: string) => void;
    connect: boolean;
    selectedItem: any;
    setSelectedItem: any;
    localParentItemId: any;
    setLocalParentItemId: any;
    toggleItemConnection: any;
    loadingDatiAggiuntivi: any;
}

export default function DatiAggiuntivi(props: IProps) {
    const {
        fetchDatiAggiuntivi,
        connect,
        selectedItem,
        setSelectedItem,
        localParentItemId,
        setLocalParentItemId,
        toggleItemConnection,
        loadingDatiAggiuntivi,
    } = props;
    const { t } = useTranslation();
    const { data } = useCalendarData();

    const handleSelectedData = async (
        e: React.ChangeEvent<{ value: unknown }>
    ) => {
        const selectedId = e.target.value as string;
        const response = await fetchDatiAggiuntivi(selectedId);
        setSelectedItem(response);
        setLocalParentItemId(selectedId);
    };

    return (
        <Box
            sx={{
                p: 2,
                border: "1px solid #ccc",
                borderRadius: "8px",
                width: 800,
                height: 300,
                ml: "15px",
                position: "relative", // This is important for centering the spinner
            }}
        >
            {loadingDatiAggiuntivi ? (
                <Box
                    sx={{
                        position: "absolute", // Ensure spinner is positioned over the content
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        zIndex: 1, // Ensures spinner is above other content
                    }}
                >
                    <Spinner fullPage={false} />
                </Box>
            ) : (
                <>
                    {/* Notice */}
                    <Typography variant="body2" sx={{ mb: 2 }}>
                        <strong>{t("N.B.")}</strong>
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 3 }}>
                        {t(`Un Impegno può essere collegato solo a oggetti di tipo
                        "Libero".`)}
                    </Typography>

                    {/* Oggetto and Collega Section */}
                    <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
                        {connect ? (
                            <Typography>
                                {t(`Questo Impegno Sarà collegato a `)}
                                <strong>{selectedItem?.tipologia}</strong>
                                <strong>{` - `}</strong>
                                <strong>{selectedItem?.nome}</strong>
                                {t(` Alla conferma.`)}
                            </Typography>
                        ) : (
                            <FormControl sx={{ flex: 1 }}>
                                <Select
                                    value={localParentItemId}
                                    onChange={(e: any) => handleSelectedData(e)}
                                >
                                    {(data?.additionalItems || [])?.map(
                                        (data: any, index: number) => {
                                            return (
                                                <MenuItem
                                                    key={index}
                                                    value={data.id}
                                                >
                                                    {`${data.tipo} - ${data.nome}`}
                                                </MenuItem>
                                            );
                                        }
                                    )}
                                </Select>
                            </FormControl>
                        )}
                        <Button
                            variant="contained"
                            sx={{ ml: 2 }}
                            disabled={selectedItem === null}
                            onClick={() =>
                                toggleItemConnection(localParentItemId)
                            }
                            color={connect ? "error" : "primary"}
                        >
                            {connect ? t("Scollega") : t("Collega")}
                        </Button>
                    </Box>

                    <Divider sx={{ my: 3 }} />

                    {/* Details Section */}
                    <Box>
                        <Typography variant="body2" sx={{ mt: 1 }}>
                            <strong>Nome:</strong> {selectedItem?.nome || "-"}
                        </Typography>
                        <Typography variant="body2" sx={{ mt: 1 }}>
                            <strong>Tipologia:</strong>{" "}
                            {selectedItem?.tipologia || "-"}
                        </Typography>
                        <Typography variant="body2" sx={{ mt: 1 }}>
                            <strong>Data:</strong> {selectedItem?.data || "-"}
                        </Typography>
                        <Typography variant="body2" sx={{ mt: 1 }}>
                            <strong>Referente:</strong>{" "}
                            {selectedItem?.referente || "-"}
                        </Typography>
                    </Box>
                </>
            )}
        </Box>
    );
}
