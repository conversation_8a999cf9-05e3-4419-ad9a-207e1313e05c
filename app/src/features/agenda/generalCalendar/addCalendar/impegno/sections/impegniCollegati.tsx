import { Box } from "@vapor/react-material";
import { CustomDataGrid } from "../../../../../../custom-components/CustomDataGrid";
import useFetchImpegniCollegati from "../hooks/useFetchImpegniCollegati";

export default function ImpegniCollegati() {
    const { listImpegniCollegati } = useFetchImpegniCollegati();
    const renderDataTable = () => {
        return (
            <CustomDataGrid
                name="impegniCollegati"
                columns={listImpegniCollegati.columns}
                data={listImpegniCollegati.rows || []}
                page={listImpegniCollegati.page}
                totalRows={listImpegniCollegati.totalRows}
                pageSize={listImpegniCollegati.pageSize}
                onPageChangeCallback={() => ""}
            />
        );
    };

    return (
        <Box ml={2} mr={1}>
            {renderDataTable()}
        </Box>
    );
}
