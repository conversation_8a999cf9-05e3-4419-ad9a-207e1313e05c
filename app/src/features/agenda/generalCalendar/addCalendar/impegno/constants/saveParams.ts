import { IDeadlineParams } from "../interfaces/impegno.interface";
import { CURRENT_DATE_FORMATTED, DEADLINE_UNIQUE_RANDOM } from "./constant";

export const DEFAULT_DEADLINE_STATUS = '0';
export const DEFAULT_DEADLINE_CATEGORY = '0';

export const DEFAULT_DEADLINE_PARAMS: IDeadlineParams = {
    deadlineUniqueid: "",
    deadlineLinkuid: "",
    deadlineFileUniqueid: "",
    templates: "",
    dynamic: "0",
    macroInstanceUid: "",
    recurrenceId: "",
    connectedElements: "",
    idPratica: "",
    deadlineText: "", //required
    deadlineDate: CURRENT_DATE_FORMATTED(),
    deadlineHours: "09",
    deadlineMinutes: "00",
    deadlineDaysBefore: 0,
    deadlinePeriod: "0",
    deadlineOnorariDiritti: "0",
    deadlineSpeseesenti: "0",
    deadlineSpeseimponibili: "0",
    deadlineSpeseescluse: "0",
    deadlineType: "",
    deadlineCategory: DEFAULT_DEADLINE_CATEGORY,
    deadlineStatus: DEFAULT_DEADLINE_STATUS,
    deadLinesGroups: "-1",
    deadlineUser: [],
    deadlineAnnotation: "",
    deadlineAddebitabile: true,
    parentItemId: "",
    nuovoImpegno: "0",
    deadlineUniqueidToDeclare: DEADLINE_UNIQUE_RANDOM,
    deadlineVisible: false,
    endType: "BYOCCURRENCES",
    deadlineEvasa: false,
    deadlineNonevadere: false,
    deadlineImportant: false,
    deadlineBillable: false,
    deadlineFile: "",
    rirtemplate: "daily",
    ridailyinterval: "1",
    occurrencesN: "1",
    riweeklyinterval: "1",
    rimonthlyinterval: "1",
    rimonthlydayofmonthday: "1",
    riyearlyinterval: "1",
    riyearlydayofmonthmonth: "1",
    rirangebyenddatecalendar: CURRENT_DATE_FORMATTED(),
};
