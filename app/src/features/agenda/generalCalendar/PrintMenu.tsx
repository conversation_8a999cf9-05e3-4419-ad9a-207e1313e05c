import { useTranslation } from "@1f/react-sdk";
import { faCaretDown, faPrint } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Menu,
    MenuItem,
    Radio,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow,
} from "@vapor/react-material";
import { useEffect, useState } from "react";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import { useGetPrintTemplates } from "./hooks/useGetPrintTemplates";
import { usePrintCalendarData } from "./hooks/usePrintCalendarData";
import { usePrintCalendarTemplate } from "./hooks/usePrintCalendarTemplate";
import { IPrintTemplatesRespose } from "./typings/generalCalendar.interface";

interface PrintMenuProps {
    query: {
        calendarCommitments: any;
        deadlineType: any;
        deadlineCategory: any;
        calendarPerson: any;
        calendarGroup: any;
        calendarReferent: any;
        praticaSelectUniqueid: any;
        authority: any;
        authoritySearch?: string;
        calendarEvasa: any;
        calendarNonevadere: any;
        viewName: string;
        start: number;
        end: number;
        closeDeadline: boolean;
    };
    hasEvents: boolean;
}

export const PrintMenu = ({ query, hasEvents }: PrintMenuProps) => {
    const { t } = useTranslation();
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [noEventsWarning, setNoEventsWarning] = useState(false);
    const [openTemplateMenu, setOpenTemplateMenu] = useState(false);
    const [selected, setSelected] = useState<string | null>(null);
    const open = Boolean(anchorEl);

    const [printData, setPrintData] = useState(false);
    const [printTemplate, setPrintTemplate] = useState(false);
    const [isDeadlineQuery, setIsDeadlineQuery] = useState<any>(null);
    const [templates, setTemplates] = useState<IPrintTemplatesRespose[] | null>(
        null
    );

    const templatesResponse = useGetPrintTemplates({
        fetch: openTemplateMenu,
    });

    useEffect(() => {
        if (templatesResponse.hasLoaded) {
            setTemplates(templatesResponse.data);
        }
    }, [templatesResponse.hasLoaded]);

    const selectedTemplate =
        templates &&
        templates.filter((template: any) => template.id === selected)[0];

    const createQuery = (closeDeadlineValue: boolean, isTemplate: boolean) => ({
        ...(isTemplate && {
            sectionid: "",
            category: selectedTemplate ? selectedTemplate.category : "",
            docid: selectedTemplate ? selectedTemplate.id : "",
        }),
        calendarCommitments: query.calendarCommitments,
        deadlineType: query.deadlineType,
        deadlineCategory: query.deadlineCategory,
        calendarPerson: query.calendarPerson,
        calendarGroup: query.calendarGroup,
        calendarReferent: query.calendarReferent,
        praticaSelectUniqueid: query.praticaSelectUniqueid,
        authority: query.authority,
        authoritySearch: query.authoritySearch || "",
        calendarEvasa: query.calendarEvasa,
        calendarNonevadere: query.calendarNonevadere,
        viewName: query.viewName,
        visStart: Number(query.start) * 1000,
        visEnd: Number(query.end) * 1000,
        closeDeadline: closeDeadlineValue,
    });

    const printResponse = usePrintCalendarData({
        query: isDeadlineQuery || createQuery(query.closeDeadline, false),
        print: printData,
    });

    const printTemplateResponse = usePrintCalendarTemplate({
        query: isDeadlineQuery || createQuery(query.closeDeadline, true),
        print: printTemplate,
    });

    useEffect(() => {
        if (printResponse.hasLoaded || printTemplateResponse.hasLoaded) {
            setPrintData(false);
            setPrintTemplate(false);
            setOpenTemplateMenu(false);
            setIsDeadlineQuery(null);
        }
    }, [
        printResponse.hasLoaded,
        printResponse.loading,
        printTemplateResponse.hasLoaded,
        printTemplateResponse.loading,
    ]);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleMenuItemClick = (
        value: "closeDeadlineTrue" | "closeDeadlineFalse" | "printTemplates"
    ) => {
        if (!hasEvents) {
            setNoEventsWarning(true);
            handleClose();
            return;
        }

        switch (value) {
            case "closeDeadlineFalse":
                setIsDeadlineQuery(createQuery(false, false));
                setPrintData(true);
                break;
            case "closeDeadlineTrue":
                setIsDeadlineQuery(createQuery(true, false));
                setPrintData(true);
                break;
            case "printTemplates":
                setOpenTemplateMenu(true);
                break;
        }

        handleClose();
    };

    const handleTemplateConfirm = () => {
        if (selected) {
            setIsDeadlineQuery(createQuery(query.closeDeadline, true));
            setPrintTemplate(true);
        }
    };

    return (
        <>
            <ConfirmModal
                open={noEventsWarning}
                agree="Ok"
                decline=""
                title=""
                handleAgree={() => setNoEventsWarning(false)}
                handleDecline={() => setNoEventsWarning(false)}
                confirmText={t("Non è presente nessun evento da stampare")}
            />
            <Dialog open={openTemplateMenu}>
                <DialogTitle>
                    {t("Scegli il template da utilizare")}
                </DialogTitle>
                <DialogContent>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell></TableCell>
                                <TableCell>{t("Nome")}</TableCell>
                                <TableCell>{t("Descrizione")}</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {templates &&
                                templates.map((response) => (
                                    <TableRow key={response.id}>
                                        <TableCell>
                                            <Radio
                                                value={response.id}
                                                checked={
                                                    selected === response.id
                                                }
                                                onClick={() =>
                                                    setSelected(response.id)
                                                }
                                            />
                                        </TableCell>
                                        <TableCell>
                                            {response.filename}
                                        </TableCell>
                                        <TableCell>{response.title}</TableCell>
                                    </TableRow>
                                ))}
                        </TableBody>
                    </Table>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpenTemplateMenu(false)}>
                        {t("Annulla")}
                    </Button>
                    <Button variant="outlined" onClick={handleTemplateConfirm}>
                        {t("Conferma")}
                    </Button>
                </DialogActions>
            </Dialog>
            <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
                <MenuItem
                    key={1}
                    onClick={() => handleMenuItemClick("closeDeadlineTrue")}
                >
                    {t("Stampa")}
                </MenuItem>
                <MenuItem
                    key={2}
                    onClick={() => handleMenuItemClick("closeDeadlineFalse")}
                >
                    {t("Stampa sensa avvisi")}
                </MenuItem>
                <MenuItem
                    key={3}
                    onClick={() => handleMenuItemClick("printTemplates")}
                >
                    {t("Stampa template")}
                </MenuItem>
            </Menu>
            <Button
                onClick={handleClick}
                variant="outlined"
                startIcon={<FontAwesomeIcon icon={faPrint} />}
                endIcon={<FontAwesomeIcon icon={faCaretDown} />}
            >
                {t("Stampa")}
            </Button>
        </>
    );
};
