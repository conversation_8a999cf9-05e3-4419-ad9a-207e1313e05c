import React, { useState } from "react";
import {
    Box,
    Button,
    TextField,
    Checkbox,
    FormControlLabel,
    FormControl,
    Select,
    MenuItem,
    Autocomplete,
    Badge,
    Typography,
    Grid,
    IconButton,
} from "@vapor/react-material";
import LegendToggleIcon from "@mui/icons-material/LegendToggle";
import Outlookicon from "./../../../assets/images/bulletOutlook.png";
import Googleicon from "./../../../assets/images/bulletGoogle.png";
import Policwebicon from "./../../../assets/images/icon-polisweb-black.png";
import SquareIcon from "@mui/icons-material/Square";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import {
    calendarCommitmentsList,
    calendarEvasaList,
    calendarNonevadereList,
} from "./helpers/selectFilterData";
import { LegendTooltip } from "./helpers/tooltipCustomHelper";
import SearchAuthority from "./hooks/useSearchAuthority";
import { ICalendarFilterProps } from "./typings/generalCalendar.interface";
import { useNavigate } from "react-router-dom";
import { gettingCalendarViewName } from "./helpers/gettingCalendarViewName";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import { updateCalendar } from "./helpers/calendarHelper";
import moment from "moment";

const ICON = <CheckBoxOutlineBlankIcon fontSize="small" />;
const CHECKED_ICON = <CheckBoxIcon fontSize="small" />;

export default function Filters(props: ICalendarFilterProps) {
    const navigate = useNavigate();
    const {
        query,
        setQuery,
        DEFAULT_QUERY,
        calendarData,
        t,
        calendarRef,
        setMonthTitle,
    } = props;
    const [authorityName, setAuthorityName] = useState<string>("");
    const { handleArchiveSearch, searchResult, authoriyLoading } =
        SearchAuthority();

    const [intestatariOpen, setIntestatariOpen] = useState(false);
    const [referentOpen, setReferentOpen] = useState(false);

    const clearAll = () => {
        try {
            const calendarApi: any = calendarRef.current?.getApi();

            if (!calendarApi || !calendarApi.currentData || !calendarApi.currentDataManager) {
                console.warn('Calendar API not properly initialized for clearAll');
                const fallbackQuery = {
                    ...(DEFAULT_QUERY as any),
                    calendarPersons: [],
                    start: moment().startOf('month').unix(),
                    end: moment().endOf('month').unix(),
                    date: moment().unix(),
                };
                setAuthorityName("");
                setQuery(fallbackQuery);
                return;
            }

            const calendarViewName = gettingCalendarViewName(
                calendarApi.currentData.currentViewType
            );

            const activeRange = calendarApi.currentDataManager.state?.dateProfile?.activeRange;
            const currentDate = calendarApi.getDate();

            if (!activeRange || !activeRange.start || !activeRange.end || !currentDate) {
                console.warn('Calendar date range not properly initialized');
                const now = moment();
                const fallbackQuery = {
                    ...(DEFAULT_QUERY as any),
                    start: now.startOf('month').unix(),
                    end: now.endOf('month').unix(),
                    date: now.unix(),
                    viewName: calendarViewName,
                    calendarPersons: [],
                };
                setAuthorityName("");
                setQuery(fallbackQuery);
                return;
            }

            const newStart = activeRange.start.getTime() / 1000;
            const newEnd = activeRange.end.getTime() / 1000;
            const newDate = currentDate.getTime() / 1000;

            // Clear localStorage filters
            localStorage.removeItem("calendarCurrentDate");
            localStorage.removeItem("agendaQuery");
            localStorage.setItem("cleared", "true");

            const updatedQuery: any = {
                ...DEFAULT_QUERY, //this is not updated to query
                start: newStart,
                end: newEnd,
                date: newDate,
                viewName: calendarViewName,
                calendarPersons: [], // Maintain users when clearing filters
            };

            // Reset related states
            setAuthorityName("");
            setQuery(updatedQuery);

            // Update the calendar view with the cleared filters
            updateCalendar(
                calendarApi,
                updatedQuery,
                setQuery,
                setMonthTitle,
                t,
                calendarViewName
            );
        } catch (error) {
            console.error('Error in clearAll:', error);
            setAuthorityName("");
            setQuery(DEFAULT_QUERY as any);
        }
    };

    const onChangeInputs = (e: any) => {
        const { name, value } = e.target;
        setQuery({ ...query, [name]: value });
    };

    const onChangeCheckboxes = (e: any) => {
        const { name, checked } = e.target;
        const calendarAPI = calendarRef?.current?.getApi();
        if (name === 'calendarWeekends' && calendarAPI) {
            // If currently in work week view, switch to regular week view when showing weekends
            if (checked && calendarAPI.view.type === 'timeGridWeek' && !calendarAPI.getOption('weekends')) {
                calendarAPI.setOption('weekends', true);
            }
        }
        setQuery({ ...query, [name]: checked });
    };

    const convertStartDate = (dateString: string) =>
        dateString.split("/").reverse().join("/");
    const convertEndDate = (dateString: string) =>
        dateString.split("-").reverse().join("/");

    const handleNavigateToCommitments = () => {
        const startDate = convertStartDate(calendarData.startButtonDate);
        const endDate = convertEndDate(calendarData.endButtonDate);
        const navigateQuery: string = `/legacy/deadlines/deadlines?oldDeadlines=${true}&startDate=${startDate}&endDate=${endDate}&intestatario=${calendarData.calendarCommitments
            }`;
        navigate(navigateQuery);
    };

    const handleOptionChange = (_: any, newValue: any) => {
        if (newValue) {
            const selectedItem = searchResult.find(
                (item) => item.nome === newValue
            );
            if (selectedItem) {
                setQuery({ ...query, authoritySearchId: selectedItem.id });
                setAuthorityName(selectedItem.nome);
            } else {
                console.warn(
                    "Selected item not found in search results:",
                    newValue
                );
            }
        }
    };

    const isSoloUdienzeNotSelected =
        query.calendarCommitments !== "hearingsOnly";

    return (
        <Box
            component="form"
            sx={{
                width: "100%",
                '& .MuiFormControl-root': {
                    width: '100%'
                }
            }}
        >
            <Grid container spacing={2}>
                {/* First row */}
                <Grid item md={12}>
                    <FormControl>
                        <Select
                            id="calendarCommitments"
                            name="calendarCommitments"
                            value={query.calendarCommitments}
                            onChange={onChangeInputs}
                        >
                            {calendarCommitmentsList.map((commitments: any, index: number) => (
                                <MenuItem key={index} value={commitments.value}>
                                    {commitments.name}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </Grid>

                {isSoloUdienzeNotSelected && (
                    <>
                        <Grid item md={12}>
                            <FormControl>
                                <Select
                                    id="calendarDeadlineType"
                                    name="calendarDeadlineType"
                                    value={query.calendarDeadlineType}
                                    onChange={onChangeInputs}
                                >
                                    <MenuItem key="-1" value="-1">
                                        {t("Tutti le tipologie")}
                                    </MenuItem>
                                    {(calendarData.deadlineTypes || []).map((group: any, index: number) => (
                                        <MenuItem key={index} value={group.id}>
                                            {group.nome}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid item md={12}>
                            <FormControl>
                                <Select
                                    id="calendarDeadlineCategory"
                                    name="calendarDeadlineCategory"
                                    value={query.calendarDeadlineCategory}
                                    onChange={onChangeInputs}
                                >
                                    <MenuItem key="-1" value="-1">
                                        {t("Tutti le categorie")}
                                    </MenuItem>
                                    {(calendarData.deadlineCategories || []).map(
                                        (group: any, index: number) => (
                                            <MenuItem key={index} value={group.id}>
                                                {group.nome}
                                            </MenuItem>
                                        )
                                    )}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid item md={12}>
                            <Autocomplete
                                selectOnFocus
                                sx={{ width: '100%' }}
                                loadingText={t("Caricamento…")}
                                noOptionsText={t("Nessuna opzione")}
                                open={intestatariOpen}
                                onOpen={() => setIntestatariOpen(true)}
                                onClose={() => setIntestatariOpen(false)}
                                componentsProps={{
                                    popupIndicator: {
                                        title: intestatariOpen ? t("Chiudi") : t("Apri"),
                                    },
                                }}
                                clearOnBlur
                                multiple
                                value={(calendarData.users || []).filter(
                                    (user: any) => query.calendarPersons.includes(user.id)
                                )}
                                options={calendarData.users || []}
                                getOptionLabel={(option: any) => option.nomeutente || ""}
                                onChange={(_event, newValue) => {
                                    const selectedIds = newValue.map(
                                        (option: any) => option.id
                                    );
                                    setQuery({
                                        ...query,
                                        calendarPersons: selectedIds,
                                    });
                                }}
                                isOptionEqualToValue={(option: any, value: any) =>
                                    option.id === value.id
                                }
                                renderOption={(props, option: any) => (
                                    <li {...props}>{option.nomeutente}</li>
                                )}
                                renderInput={(params: any) => (
                                    <TextField
                                        {...params}
                                        placeholder={t("Seleziona gli intestatari")}
                                    />
                                )}
                            />
                        </Grid>
                    </>
                )}

                <Grid item md={12}>
                    <FormControl>
                        <Select
                            id="calendarGroup"
                            name="calendarGroup"
                            value={query.calendarGroup}
                            onChange={onChangeInputs}
                        >
                            <MenuItem key="-1" value="-1">
                                {t("Tutti i gruppi")}
                            </MenuItem>
                            {(calendarData.groups || []).map(
                                (group: any, index: number) => (
                                    <MenuItem key={index} value={group.id}>
                                        {group.name}
                                    </MenuItem>
                                )
                            )}
                        </Select>
                    </FormControl>
                </Grid>

                <Grid item md={12}>
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="onlyGroupEvents"
                                checked={query.onlyGroupEvents}
                                onChange={onChangeCheckboxes}
                            />
                        }
                        label={t("Solo eventi di Gruppo")}
                    />
                </Grid>

                {query.calendarCommitments !== "deadlinesOnly" && (
                    <Grid item md={12}>
                        <Autocomplete
                            disableCloseOnSelect
                            clearOnEscape
                            sx={{ width: '100%' }}
                            loadingText={t("Caricamento…")}
                            noOptionsText={t("Nessuna opzione")}
                            open={referentOpen}
                            onOpen={() => setReferentOpen(true)}
                            onClose={() => setReferentOpen(false)}
                            multiple
                            options={calendarData.referents || []}
                            value={(calendarData.referents || []).filter(
                                (user: any) => query.calendarReferents.includes(user.id)
                            )}
                            onChange={(_event, newValue) => {
                                const selectedIds = newValue.map(
                                    (option: any) => option.id
                                );
                                setQuery({
                                    ...query,
                                    calendarReferents: selectedIds,
                                });
                            }}
                            isOptionEqualToValue={(option: any, value: any) =>
                                option.id === value.id
                            }
                            getOptionLabel={(option: any) => option.nomeutente}
                            renderInput={(params: any) => (
                                <TextField
                                    {...params}
                                    placeholder={t("Seleziona gli referenti")}
                                />
                            )}
                            renderOption={(props: any, option: any) => {
                                const isChecked = query.calendarReferents.includes(option.id);
                                return (
                                    <li {...props} key={option.nomeutente}>
                                        <Checkbox
                                            icon={ICON}
                                            checkedIcon={CHECKED_ICON}
                                            style={{ marginRight: 8 }}
                                            checked={isChecked}
                                        />
                                        {option.nomeutente}
                                    </li>
                                );
                            }}
                        />
                    </Grid>
                )}

                <Grid item md={12}>
                    <TextField
                        fullWidth
                        placeholder={t("Cerca pratica per codice, descrizione, nominativi, RG…")}
                        id="pratica"
                        value={query.pratica}
                        onChange={onChangeInputs}
                        name="pratica"
                    />
                </Grid>

                <Grid item md={12}>
                    <Autocomplete
                        selectOnFocus
                        clearOnBlur
                        sx={{ width: '100%' }}
                        handleHomeEndKeys
                        freeSolo
                        loading={authoriyLoading}
                        value={authorityName}
                        loadingText={t("Caricamento…")}
                        noOptionsText={t("Nessuna opzione")}
                        onInputChange={(_: any, newInputValue: any) =>
                            handleArchiveSearch(newInputValue)
                        }
                        options={searchResult.map((result: any) => result.nome)}
                        getOptionLabel={(option: any) => option}
                        onChange={handleOptionChange}
                        renderOption={(props: any, option: any) => (
                            <div {...props}>
                                <span dangerouslySetInnerHTML={{ __html: option }} />
                            </div>
                        )}
                        renderInput={(params: any) => (
                            <TextField
                                {...params}
                                placeholder={t("Filtra per autorità...")}
                            />
                        )}
                    />
                </Grid>

                <Grid item md={12}>
                    <FormControl>
                        <Select
                            id="calendarEvasa"
                            name="calendarEvasa"
                            value={query.calendarEvasa}
                            onChange={onChangeInputs}
                        >
                            {calendarEvasaList.map((evasa: any, index: number) => (
                                <MenuItem key={index} value={evasa.value}>
                                    {evasa.name}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </Grid>

                <Grid item md={12}>
                    <FormControl>
                        <Select
                            id="calendarNonevadere"
                            name="calendarNonevadere"
                            value={query.calendarNonevadere}
                            onChange={onChangeInputs}
                        >
                            {calendarNonevadereList.map(
                                (nonevadere: any, index: number) => (
                                    <MenuItem key={index} value={nonevadere.value}>
                                        {nonevadere.name}
                                    </MenuItem>
                                )
                            )}
                        </Select>
                    </FormControl>
                </Grid>

                <Grid item md={12}>
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="calendarWeekends"
                                checked={query.calendarWeekends}
                                onChange={onChangeCheckboxes}
                            />
                        }
                        label={t("Mostra weekend")}
                    />
                </Grid>

                {/* Action Buttons */}
                <Grid item md={12}>
                    <Button
                        variant="outlined"
                        color="primary"
                        onClick={clearAll}
                        fullWidth
                    >
                        {t("Mostra tutti")}
                    </Button>
                </Grid>

                <Grid item md={12}>
                    <Badge
                        badgeContent={calendarData.missedEvents && Number(calendarData.missedEvents)}
                        sx={{ width: '100%' }}
                    >
                        <Button
                            variant="outlined"
                            onClick={handleNavigateToCommitments}
                            color="primary"
                            fullWidth
                        >
                            {t("Impegni passati non evasi")}
                        </Button>
                    </Badge>
                </Grid>

                <Grid item md={12}>
                    <LegendTooltip
                        title={
                            <React.Fragment>
                                <Grid container spacing={2}>
                                    <Grid item xs={12}>
                                        <div
                                            style={{
                                                display: "flex",
                                                alignItems: "center",
                                                marginBottom: 1,
                                            }}
                                        >
                                            <IconButton
                                                disabled
                                                sx={{
                                                    mr: 1,
                                                    width: 32,
                                                    height: 32,
                                                }}
                                            >
                                                <SquareIcon color="warning" />
                                            </IconButton>
                                            <Typography variant="body2">
                                                {t("Avviso d'impegno")}
                                            </Typography>
                                        </div>
                                        <div
                                            style={{
                                                display: "flex",
                                                alignItems: "center",
                                                marginBottom: 1,
                                            }}
                                        >
                                            <IconButton
                                                disabled
                                                sx={{
                                                    mr: 1,
                                                    width: 32,
                                                    height: 32,
                                                }}
                                            >
                                                <SquareIcon color="info" />
                                            </IconButton>
                                            <Typography variant="body2">
                                                {t("Udienza")}
                                            </Typography>
                                        </div>
                                        <div
                                            style={{
                                                display: "flex",
                                                alignItems: "center",
                                                marginBottom: 1,
                                            }}
                                        >
                                            <IconButton
                                                disabled
                                                sx={{
                                                    mr: 1,
                                                    width: 32,
                                                    height: 32,
                                                }}
                                            >
                                                <img
                                                    src={Policwebicon}
                                                    alt="Policweb"
                                                    className="calendarLegendIcon"
                                                />
                                            </IconButton>
                                            <Typography variant="body2">
                                                {t("Impegno Polisweb")}
                                            </Typography>
                                        </div>
                                        <div
                                            style={{
                                                display: "flex",
                                                alignItems: "center",
                                                marginBottom: 1,
                                            }}
                                        >
                                            <IconButton
                                                sx={{ color: "warning.main" }}
                                            >
                                                <ErrorOutlineIcon />
                                            </IconButton>
                                            <Typography variant="body2">
                                                {t("Impegno importante")}
                                            </Typography>
                                        </div>
                                        <hr />
                                        <div
                                            style={{
                                                display: "flex",
                                                alignItems: "center",
                                                marginBottom: 1,
                                            }}
                                        >
                                            <IconButton
                                                sx={{ width: 24, height: 24 }}
                                            >
                                                <img
                                                    src={Googleicon}
                                                    alt="Google"
                                                    className="calendarLegendIcon"
                                                />
                                            </IconButton>
                                            <Typography variant="body2">
                                                {t("Sincronizzazione Google")}
                                            </Typography>
                                        </div>
                                        <div
                                            style={{
                                                display: "flex",
                                                alignItems: "center",
                                                marginBottom: 1,
                                            }}
                                        >
                                            <IconButton
                                                sx={{ width: 24, height: 24 }}
                                            >
                                                <img
                                                    src={Outlookicon}
                                                    alt="Outlook"
                                                    className="calendarLegendIcon"
                                                />
                                            </IconButton>
                                            <Typography variant="body2">
                                                {t("Sincronizzazione Outlook")}
                                            </Typography>
                                        </div>
                                    </Grid>
                                </Grid>
                            </React.Fragment>
                        }
                    >
                        <Button
                            variant="contained"
                            endIcon={<LegendToggleIcon />}
                            color="secondary"
                            fullWidth
                        >
                            {t("Legenda")}
                        </Button>
                    </LegendTooltip>
                </Grid>
            </Grid>
        </Box>
    );
}
