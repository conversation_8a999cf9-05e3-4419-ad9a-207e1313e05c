import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useDownloadDocument = ({
    uniqueid,
}: {
    uniqueid: string | undefined | null;
    download: boolean;
}) => {
    const { loading, doFetch, hasLoaded } = usePostCustom("documents/getFile");

    useEffect(() => {
        doFetch(true, { uniqueid: uniqueid });
    });

    return { loading, hasLoaded };
};
