import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../custom-components/PageTitle";
import { <PERSON><PERSON>, But<PERSON> } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { VaporToolbar } from "@vapor/react-custom";
import { Tab, Tabs } from "@vapor/react-extended";
import { useEffect, useRef, useState } from "react";
import { useGetDocumentDetails } from "./hooks/GetDocumentDetails";
import { useParams, useNavigate } from "react-router-dom";
import SpinnerButton from "../../../custom-components/SpinnerButton";
import { useMoveDocumentOpenText } from "./hooks/MoveDocumentOpenText";
import { useMoveDocument } from "./hooks/MoveDocument";
import { ModifyDocumentDetails } from "../../../interfaces/documents.interface";
import { useSendEmail } from "./hooks/SendMail";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import { useGetDocumentsData } from "./hooks/GetDocumentsData";
import { DocumentTab } from "./DocumentTabs/DocumentTab";
import { OtherDataTab } from "./DocumentTabs/OtherDataTab";
import { SignDocumentTab } from "./DocumentTabs/SignDocumentTab";
import { EmailDocumentTab } from "./DocumentTabs/EmailDocumentTab";
import { MoveDocumentTab } from "./DocumentTabs/MoveDocumentTab";
import { useCheckRemoteRignature } from "./hooks/CheckRemoteSign";
import { parse } from "date-fns";
import { useConvertPdf } from "./hooks/ConvertPdf";
import { useSaveDocumentDetails } from "./hooks/SaveDocumentDetails";
import { useDeleteDocument } from "./hooks/DeleteDocument";
import Spinner from "../../../custom-components/Spinner";
import { useSignDocument } from "./hooks/SignDocument";
import ToastNotification from "../../../custom-components/ToastNotification";

export const DocumentsCreateUpdate = () => {
    const { t } = useTranslation();
    const { id } = useParams();
    const navigate = useNavigate();

    const userName = useRef(null);
    const [selectedTab, setSelectedTab] = useState(0);
    const [documentDetails, setDocumentDetails] = useState<
        undefined | ModifyDocumentDetails
    >();

    const [lastEditor, setLastEditor] = useState<{
        modificatoda: string;
        modificatoil: string;
    }>();

    const [confirmConvertPDF, setConfirmConvertPDF] = useState(false);
    const [moveDocumentOpenText, setMoveDocumentOpenText] = useState(false);
    const [showDeleteFileModal, setShowDeleteFileModal] = useState(false);
    const [saveDocument, setSaveDocument] = useState(false);
    const [showRename, setShowRename] = useState(false);
    const [fromDatabaseFileUniqueId, setFromDatabaseFileUniqueId] =
        useState("");

    const [deleteFile, setDeleteFile] = useState(false);
    const [convertPdf, setConvertPdf] = useState(false);

    const [documentStatus, setDocumentStatus] = useState<any>([]);

    const { hasLoaded, data } = useGetDocumentsData();

    useEffect(() => {
        if (hasLoaded) {
            setDocumentStatus(data.documentstatus);
            userName.current = data.loggedUser.nomeutente;
        }
    }, [hasLoaded]);

    ///Move Document
    const [moveDocument, setMoveDocument] = useState(false);
    const [category, setCategory] = useState("");
    const [inputValue, setInputValue] = useState("");
    const [value, setValue] = useState<string | null>("");
    const [successNotification, setSuccessNotification] = useState(false);

    const moveDocumentsResponse = useMoveDocument({
        archiveSearchTarget: category,
        fileUid: documentDetails?.documentFileUniqueid ?? "",
        fileuniqueid: "",
        uniqueid: id,
        move: moveDocument,
    });

    useEffect(() => {
        if (moveDocumentsResponse.hasLoaded) {
            setMoveDocument(false);
        }
    }, [moveDocumentsResponse.hasLoaded]);

    ///Signature
    const [certificateId, setCertificateId] = useState("");
    const [otpPassword, setOtpPassword] = useState("");
    const [selectedLawyer, setSelectedLawyer] = useState("");
    const [remoteSignOn, setRemoteSignOn] = useState(false);
    const [sign, setSign] = useState(false);

    const checkUserIdReponse = useCheckRemoteRignature({
        userId: selectedLawyer,
    });

    const fisrtSignREsponse = useSignDocument({ uid: id, sign: sign });

    ///Email

    const [emailData, setEmailData] = useState({
        document1: "",
        emailAccountUid: "",
        fromemail: "",
        message: "",
        saveInMessages: true,
        subject: "",
        toemail: "",
    });

    const [emailErrors, setEmailErrors] = useState<Partial<typeof emailData>>(
        {}
    );

    const validateEmail = () => {
        const updatedErrors: Partial<typeof emailData> = {};

        if (emailData.fromemail === "") {
            updatedErrors.fromemail = t("Email obbligatorio");
        }
        if (emailData.toemail === "") {
            updatedErrors.toemail = t("Email obbligatorio");
        }
        if (emailData.subject === "") {
            updatedErrors.subject = t("Oggetto obbligatorio");
        }
        if (emailData.message === "") {
            updatedErrors.message = t("Messaggio obbligatorio");
        }
        setEmailErrors(updatedErrors);
        return Object.keys(updatedErrors).length === 0;
    };

    const [sendEmail, setSendEmail] = useState(false);

    useSendEmail(emailData, sendEmail);

    const handleUpdateDocumentDetails =
        (field: keyof ModifyDocumentDetails) => (value: any) => {
            if (documentDetails !== undefined) {
                setDocumentDetails({
                    ...documentDetails,
                    [field]: value,
                });
            }
        };

    const clearErrorForField = (name: keyof typeof emailData) => {
        if (emailErrors[name]) {
            setEmailErrors((prevErrors) => ({
                ...prevErrors,
                [name]: undefined,
            }));
        }
    };

    const handleUpdate = (event: any) => {
        const { name, value, type, checked } = event.target;
        setEmailData((prevData) => ({
            ...prevData,
            [name]: type === "checkbox" ? checked : value,
        }));

        clearErrorForField(name);
    };

    const [moveDocumentProgress, setMoveDocumentProgress] = useState(false);
    const [pdfDocumentError, setPdfDocumentError] = useState(false);
    const [pdfDocumentErrorMessage, setPdfDocumentErrorMessage] = useState("");

    const moveDocumentResponse = useMoveDocumentOpenText({
        uniqueid: id,
        move: moveDocumentOpenText,
    });

    useEffect(() => {
        if (moveDocumentResponse.hasLoaded) {
            setMoveDocumentOpenText(false);
            setMoveDocumentProgress(true);
        }
    }, [moveDocumentResponse.hasLoaded]);

    const saveDocumentResponse = useSaveDocumentDetails({
        documentDetails: documentDetails,
        save: saveDocument,
    });

    useEffect(() => {
        if (saveDocumentResponse.hasLoaded) {
            setSaveDocument(false);
            navigate("/documents/documents");
        }
    }, [saveDocumentResponse.hasLoaded]);

    const deleteDocumentResponse = useDeleteDocument({
        documentFileUniqueid: documentDetails?.documentFileUniqueid,
        remove: deleteFile,
        uniqueid: id,
    });

    useEffect(() => {
        if (deleteDocumentResponse.hasLoaded) {
            navigate("/documents/documents");
        }
    }, [deleteDocumentResponse.hasLoaded]);

    const convertPdfResponse = useConvertPdf({ dUid: id, convert: convertPdf });
    useEffect(() => {
        if (convertPdfResponse.hasLoaded) {
            let result: any = convertPdfResponse.data;
            if (result != null && result == true) {
                navigate("/documents/documents");
            } else if (result == -1) {
                setPdfDocumentError(true);
                setPdfDocumentErrorMessage(
                    t(
                        "Versione del PDF non supportata, impossibile apporre l'attestazione su questo documento."
                    )
                );
            } else {
                setPdfDocumentError(true);
                setPdfDocumentErrorMessage(
                    t(
                        "Siamo spiacenti l'operazione non può essere completata. Riprovare più tardi. Grazie"
                    )
                );
            }
            setConfirmConvertPDF(false);
        }
    }, [convertPdfResponse.hasLoaded]);

    const documentDetailsResponse = useGetDocumentDetails({ uniqueId: id });

    useEffect(() => {
        if (documentDetailsResponse.hasLoaded) {
            setEmailData({
                ...emailData,
                document1: documentDetailsResponse.data.form.document1,
            });
            console.log(
                documentDetailsResponse.data.form.documentFileUniqueid ?? ""
            );
            setFromDatabaseFileUniqueId(
                documentDetailsResponse.data.form.documentFileUniqueid ?? ""
            );
            setDocumentDetails({
                titolodocumento:
                    documentDetailsResponse.data.form.titolodocumento,
                documentFileUniqueid:
                    documentDetailsResponse.data.form.documentFileUniqueid ??
                    "",
                nomefileForBtn:
                    documentDetailsResponse.data.form.nomefileForBtn,
                nomefileForEmail:
                    documentDetailsResponse.data.form.nomefileForEmail,
                documentoData: parse(
                    documentDetailsResponse.data.form.documentoData,
                    "dd/MM/yyyy",
                    new Date()
                ),
                numeroprotocollo:
                    documentDetailsResponse.data.form.numeroprotocollo,
                documentodi: documentDetailsResponse.data.form.documentodi,
                visibile: documentDetailsResponse.data.form.visible,
                note: documentDetailsResponse.data.form.note,
                folders: documentDetailsResponse.data.folders,
                folder_id: documentDetailsResponse.data.folder_id,
                in_out: documentDetailsResponse.data.form.in_out,
                document1: documentDetailsResponse.data.form.document1,
                uniqueid: id,
                oneDriveUniqueid: "",
                docSelectionCallbackUrl: "",
                nuovonome: "",
                nomeFile: "",
                searchOggettoDocumento: "",
                object_id: "",
                externalUser: [],
                mittente: "",
                data: documentDetailsResponse.data.form.documentoData,
                status_id: -1,
                category_id:
                    documentDetailsResponse.data.form.category_id == null
                        ? 0
                        : documentDetailsResponse.data.form.category_id,
            });
            setLastEditor({
                modificatoda: documentDetailsResponse?.data?.form.modificatoda,
                modificatoil: documentDetailsResponse?.data?.form.modificatoil,
            });
        }
    }, [documentDetailsResponse.hasLoaded]);

    return (
        <VaporPage
            contentToolbar={
                <VaporToolbar
                    variant="regular"
                    contentRight={
                        selectedTab === 2 ? (
                            <Stack gap={1} direction="row">
                                <SpinnerButton
                                    isLoading={moveDocumentsResponse.loading}
                                    label={t("Sposta")}
                                    disabled={value === ""}
                                    variant="outlined"
                                    color="primary"
                                    onClick={() => setMoveDocument(true)}
                                ></SpinnerButton>
                            </Stack>
                        ) : selectedTab === 3 ? (
                            <Stack gap={1} direction="row">
                                <Button
                                    variant="outlined"
                                    color="primary"
                                    onClick={() => setSign(true)}
                                >
                                    {t("Firma")}
                                </Button>
                            </Stack>
                        ) : selectedTab === 4 ? (
                            <Stack gap={1} direction="row">
                                <Button
                                    variant="outlined"
                                    color="primary"
                                    onClick={() => {
                                        if (validateEmail()) {
                                            setSendEmail(true);
                                        }
                                    }}
                                >
                                    {t("Invia")}
                                </Button>
                            </Stack>
                        ) : (
                            <Stack gap={1} direction="row">
                                <Button
                                    variant="outlined"
                                    onClick={() => setConfirmConvertPDF(true)}
                                >
                                    {t("Converti in PDF")}
                                </Button>
                                <Button
                                    variant="outlined"
                                    onClick={() =>
                                        setMoveDocumentOpenText(true)
                                    }
                                >
                                    {t("Sposta su openText")}
                                </Button>
                                <Button
                                    variant="outlined"
                                    color="error"
                                    onClick={() => setShowDeleteFileModal(true)}
                                >
                                    {t("Elimina")}
                                </Button>
                                {fromDatabaseFileUniqueId !== "" && (
                                    <Button
                                        variant="outlined"
                                        onClick={() =>
                                            navigate(
                                                `/legacy/archive/summary?uid=${documentDetails?.document1}`
                                            )
                                        }
                                    >
                                        {t("Vai alla pratica")}
                                    </Button>
                                )}
                                <Button
                                    variant="outlined"
                                    onClick={() => setSaveDocument(true)}
                                >
                                    {t("Salva")}
                                </Button>
                                {id !== undefined && (
                                    <Button
                                        variant="outlined"
                                        onClick={() => setShowRename(true)}
                                    >
                                        {t("Rinomina")}
                                    </Button>
                                )}
                            </Stack>
                        )
                    }
                />
            }
        >
            <PageTitle
                title="GESTIONE DOCUMENTI"
                pathToPrevPage="/documents/documents"
            ></PageTitle>
            <ConfirmModal
                agree={t("Elimina")}
                decline={t("Annulla")}
                handleAgree={() => {
                    setDeleteFile(true);
                }}
                handleDecline={() => {
                    setShowDeleteFileModal(false);
                }}
                open={showDeleteFileModal}
                title={t("Elimina documento")}
                colorConfirmButton="error"
                colorDeclineButton="primary"
                confirmText={t(
                    "Eliminare definitivamente il documento e tutti i suoi messaggi?"
                )}
                dividerVariant="fullWidth"
            />

            <ConfirmModal
                agree={t("ok")}
                decline={t("Annulla")}
                handleAgree={() => {
                    setConvertPdf(true);
                }}
                handleDecline={() => {
                    setConfirmConvertPDF(false);
                }}
                open={confirmConvertPDF}
                title={t("Conversione documento")}
                colorConfirmButton="info"
                colorDeclineButton="primary"
                confirmText={t(
                    "L'utente è tenuto a VERIFICARE che il documento risultante della conversione sia conforme all'originale."
                )}
                dividerVariant="fullWidth"
            />
            <ToastNotification
                severity="info"
                showNotification={moveDocumentProgress}
                text={t("Spostamento su OpenText in corso attendere prego")}
                setShowNotification={setMoveDocumentProgress}
            />

            <ToastNotification
                severity="error"
                showNotification={pdfDocumentError}
                text={pdfDocumentErrorMessage}
                setShowNotification={setPdfDocumentError}
            />

            <Tabs
                size="extraSmall"
                variant="standard"
                value={selectedTab}
                onChange={(_e: any, value: any) => setSelectedTab(value)}
            >
                <Tab value={0} label={t("Scheda documento")} />
                <Tab value={1} label={t("Altri dati")} />
                <Tab value={2} label={t("Sposta documento")} />
                <Tab value={3} label={t("Firma documento")} />
                <Tab value={4} label={t("Invio documento tramite email")} />
            </Tabs>
            {!hasLoaded ? (
                <Spinner></Spinner>
            ) : (
                <VaporPage.Section>
                    {selectedTab === 0 &&
                        hasLoaded &&
                        documentDetails !== undefined && (
                            <DocumentTab
                                userName={userName}
                                documentDetails={documentDetails}
                                documentData={data}
                                handleUpdateDocumentDetails={
                                    handleUpdateDocumentDetails
                                }
                                showRename={showRename}
                                fromDatabaseFileUniqueId={
                                    fromDatabaseFileUniqueId
                                }
                            />
                        )}
                    {selectedTab === 1 && hasLoaded && documentDetails && (
                        <OtherDataTab
                            documentStatus={documentStatus}
                            setDocumentStatus={setDocumentStatus}
                            documentData={data}
                            documentDetails={documentDetails}
                            handleUpdateDocumentDetails={
                                handleUpdateDocumentDetails
                            }
                        />
                    )}
                    {selectedTab === 2 && hasLoaded && documentDetails && (
                        <MoveDocumentTab
                            category={category}
                            inputValue={inputValue}
                            setCategory={setCategory}
                            setInputValue={setInputValue}
                            setValue={setValue}
                            value={value}
                            successNotification={successNotification}
                            setSuccessNotification={setSuccessNotification}
                        />
                    )}
                    {selectedTab === 3 && documentDetailsResponse.hasLoaded && (
                        <SignDocumentTab
                            certificateId={certificateId}
                            checkUserIdReponse={checkUserIdReponse}
                            otpPassword={otpPassword}
                            selectedLawyer={selectedLawyer}
                            setCertificateId={setCertificateId}
                            setOtpPassword={setOtpPassword}
                            setSelectedLawyer={setSelectedLawyer}
                            documentData={data}
                            nomefileForBtn={documentDetails?.nomefileForBtn}
                            remoteSignOn={remoteSignOn}
                            setRemoteSignOn={setRemoteSignOn}
                            setSign={setSign}
                            sign={sign}
                            uniqueid={id ?? ""}
                            fisrtSignREsponse={fisrtSignREsponse}
                        />
                    )}
                    {selectedTab === 4 && hasLoaded && documentDetails && (
                        <EmailDocumentTab
                            emailErrors={emailErrors}
                            nomefileForEmail={documentDetails?.nomefileForEmail}
                            emailData={emailData}
                            handleUpdate={handleUpdate}
                            documentData={data}
                            lastEditor={lastEditor}
                        />
                    )}
                </VaporPage.Section>
            )}
        </VaporPage>
    );
};
