import { useState, useCallback, useMemo } from "react";
import { VaporPage } from "@vapor/react-custom";
import { CustomDataGrid } from "../../custom-components/CustomDataGrid";
import {
    GridPaginationModel,
    GridRowSelectionModel,
} from "@mui/x-data-grid-pro";
import PageTitle from "../../custom-components/PageTitle";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import moment from "moment";
import Filters from "./Filters";
import useArchiveData from "./hooks/useArchiveData";
import useFilterArchive from "./hooks/useFilterArchive";
import Spinner from "../../custom-components/Spinner";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import AggiungiSoggetto from "./components/AggiungiSoggetto";
import TrasferisciUtente from "./components/TrasferisciUtente";
import AssociaWorkflow from "./components/AssociaWorkflow";
import ModificaTag from "./components/ModificaTag";
import PersonalizzaList from "./components/PersonalizzaList";
import { usePrints } from "./hooks/usePrints";
import { useUser } from "../../store/UserStore";
import { useConfigs } from "../../store/ConfigStore";
import ToastNotification from "../../custom-components/ToastNotification";
import ArchiveSummary from "./archiveSummary";
import DevisionedegliUtili from "./archiveSummary/pages/DevisionedegliUtili";
import { SIEBAR_PATHS } from "./archiveSummary/constant/sidebar.constant";
import { MergeList } from "./archiveSummary/pages/MergeList/MergeList";
import MacroPage from "./pages/MacroPage";
import SplitButton from "../../custom-components/SplitButton";
import { useGetWizards } from "./hooks/useArchiveData";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";

const Archive = () => {
    const [selectedValues, setSelectedValues] = useState<string[]>([]);
    const [selectedGridRows, setSelectedGridRows] =
        useState<GridRowSelectionModel>([]);
    const [openSoggetto, setOpenSoggetto] = useState(false);
    const [openTrasferisciUtente, setOpenTrasferisciUtente] = useState(false);
    const [openAssociaWorkflow, setOpenAssociaWorkflow] = useState(false);
    const [openModificaTag, setOpenModificaTag] = useState(false);
    const [openPersonalizzaModal, setOpenPersonalizzaModal] = useState(false);

    const { archiveData } = useArchiveData();
    const { defaultQuery, query, setQuery, list, filterArchiveData, loading } =
        useFilterArchive(archiveData);
    const { wizardsData } = useGetWizards();

    const navigate = useNavigate();
    const { t } = useTranslation();
    const { modules }: any = useUser();
    const { configs }: any = useConfigs();
    const { lawyers, relazioniutenti, workflowModels } = archiveData;
    const enableDropdownButton = !!configs?.data?.app?.wizards_bool;
 

    const {
        handleExportEtichettaPdfRequest,
        handleExportPratichePdfRequest,
        handleExportArchivePdfRequest,
        showNotification,
        setShowNotification,
        notificationText,
        notificationSeverity,
    } = usePrints(list, query);

    const onChangeFilterInputs = useCallback(
        (event: any) => {
            const { name, value } = event.target;
            setQuery((prev: any) => ({ ...prev, [name]: value }));
        },
        [setQuery]
    );

    const onChangeCheckbox = useCallback(
        (e: any) => {
            const { name, checked } = e.target;
            setQuery((prevQuery: any) => {
                const updatedQuery = { ...prevQuery };
                if (checked) {
                    updatedQuery[name] = 1;
                } else {
                    delete updatedQuery[name];
                }
                return updatedQuery;
            });
        },
        [setQuery]
    );

    const onDateChange = useCallback(
        (name: string, value: Date) => {
            const formattedDate = moment(value).format("DD/MM/YYYY");
            setQuery((prev: any) => ({
                ...prev,
                [name]: formattedDate,
            }));
        },
        [setQuery]
    );

    const handleChangeMultiSelect = useCallback(
        (_: string, values: any[]) => {
            setSelectedValues(values);
            setQuery((prev: any) => ({
                ...prev,
                lawyers: values.map((e) => e.value || e.id),
            }));
        },
        [setQuery]
    );

    const handleClickCallback = (uniqueid: any) => {
        navigate(`/archive/summary?uid=${uniqueid}`);
    };

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery((prevQuery: any) => ({
            ...prevQuery,
            page: model.page,
            pageSize: model.pageSize,
        }));
    };

    const handleRowSelectionChange = useCallback(
        (newSelectionModel: GridRowSelectionModel) => {
            setSelectedGridRows(newSelectionModel);
        },
        []
    );

    const wizardOptions = Array.isArray(wizardsData)
        ? wizardsData.map((w: any) => ({
              label: w.title,
              onClick: () => {
                  navigate(`/legacy/wizards/compile-wizard?id=${w.id}`);
              },
          }))
        : [];

    const mainButton = {
        label: t("Nuova pratica"),
        onClick: () => alert("Primary clicked!"),
        icon: AddCircleOutlineIcon,
    };

    const options = [
        ...wizardOptions,
        {
            label: t("Nuovo wizard"),
            onClick: () => navigate("/legacy/wizards/builder"),
        },
    ];

    const dataTable = useMemo(() => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                columns={list.columns}
                name="archive"
                data={list.rows}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                query={query}
                setQuery={setQuery}
                selectableRows
                onPageChangeCallback={onPageChangeCallback}
                onClickCallback={handleClickCallback}
                onRowSelectionModelChange={handleRowSelectionChange}
            />
        );
    }, [list, loading, query]);

    return (
        <>
            <VaporPage>
                <PersonalizzaList
                    fields={archiveData?.fields}
                    selectedFields={archiveData?.selectedFields}
                    selectedDynamicFields={archiveData?.selectedDynamicFields}
                    dynamicFields={archiveData?.dynamicFields}
                    open={openPersonalizzaModal}
                    setOpen={setOpenPersonalizzaModal}
                />
                <AggiungiSoggetto
                    open={openSoggetto}
                    setOpen={setOpenSoggetto}
                    title={"Aggiungi Utente"}
                    lawyers={lawyers}
                    relazioniutenti={relazioniutenti}
                    selectedGridRows={selectedGridRows}
                />
                <TrasferisciUtente
                    open={openTrasferisciUtente}
                    setOpen={setOpenTrasferisciUtente}
                    title={"Trasferisci a utente"}
                    lawyers={lawyers}
                    selectedGridRows={selectedGridRows}
                />
                <AssociaWorkflow
                    open={openAssociaWorkflow}
                    setOpen={setOpenAssociaWorkflow}
                    title={"Associa Workflow alle pratiche"}
                    workflowModels={workflowModels}
                    selectedGridRows={selectedGridRows}
                />
                <ModificaTag
                    open={openModificaTag}
                    setOpen={setOpenModificaTag}
                    title={"Modifica multipla dei Tag"}
                    selectedGridRows={selectedGridRows}
                />
                <PageTitle
                    title={t("Gestione Pratiche")}
                    showBackButton={false}
                    actionButtons={[
                        {
                            label: t("Esporta in Pdf"),
                            onclick: async () => {
                                handleExportPratichePdfRequest();
                            },
                        },
                        {
                            label: t("Esporta in csv"),
                            variant: "outlined",
                            onclick: () => "",
                            dropdown: true,
                            options: [
                                {
                                    label: t("Lista"),
                                    handleClick: () => {
                                        handleExportArchivePdfRequest(
                                            false,
                                            false,
                                            false
                                        );
                                    },
                                },
                                {
                                    label: t("Lista con campi dinamici"),
                                    handleClick: () => {
                                        handleExportArchivePdfRequest(
                                            true,
                                            false,
                                            false
                                        );
                                    },
                                },
                                {
                                    label: t("Lista udienze e impegni"),
                                    handleClick: () => {
                                        handleExportArchivePdfRequest(
                                            false,
                                            true,
                                            false
                                        );
                                    },
                                },
                                ...(configs?.data.modules
                                    ?.gestione_rischio_bool &&
                                modules &&
                                (!modules?.permissions ||
                                    (modules?.permissions[30] &&
                                        modules?.permissions[30]["r"]))
                                    ? [
                                          {
                                              label: t("Lista con rischio"),
                                              handleClick: () => {
                                                  handleExportArchivePdfRequest(
                                                      true,
                                                      false,
                                                      true
                                                  );
                                              },
                                          },
                                      ]
                                    : []),
                                ...(modules &&
                                modules?.provisioningRow?.whitelist &&
                                (Array.isArray(
                                    modules?.provisioningRow?.whitelist
                                )
                                    ? modules?.provisioningRow?.whitelist?.includes(
                                          16
                                      )
                                    : Object.values(
                                          modules?.provisioningRow?.whitelist ||
                                              {}
                                      ).includes(16))
                                    ? [
                                          {
                                              label: t("Lista con KPIs"),
                                              handleClick: () => {
                                                  handleExportArchivePdfRequest(
                                                      false,
                                                      false,
                                                      false,
                                                      true
                                                  );
                                              },
                                          },
                                      ]
                                    : []),
                                ...(configs?.data.modules?.gestione_rischio_bool
                                    ? [
                                          {
                                              label: t("Riepilogo generale"),
                                              handleClick: () => {
                                                  handleExportArchivePdfRequest(
                                                      false,
                                                      false,
                                                      false,
                                                      false,
                                                      true
                                                  );
                                              },
                                          },
                                      ]
                                    : []),
                                {
                                    label: t("Personalizza esportazione"),
                                    handleClick: () =>
                                        setOpenPersonalizzaModal(true),
                                },
                            ],
                        },
                        ...(selectedGridRows?.length > 0
                            ? [
                                  {
                                      label: t("Azioni"),
                                      onclick: () => {},
                                      dropdown: true,
                                      options: [
                                          {
                                              label: t("Aggiungi soggetto"),
                                              handleClick: () => {
                                                  setOpenSoggetto(true);
                                              },
                                          },
                                          {
                                              label: t("Trasferisci a utente"),
                                              handleClick: () => {
                                                  setOpenTrasferisciUtente(
                                                      true
                                                  );
                                              },
                                          },
                                          {
                                              label: t("Attiva Workflow"),
                                              handleClick: () => {
                                                  setOpenAssociaWorkflow(true);
                                              },
                                          },
                                          {
                                              label: t("Gestisci tag"),
                                              handleClick: () => {
                                                  setOpenModificaTag(true);
                                              },
                                          },
                                          {
                                              label: t("Etichetta"),
                                              handleClick: () =>
                                                  handleExportEtichettaPdfRequest(
                                                      selectedGridRows
                                                  ),
                                          },
                                      ],
                                      startIcon: <ArrowDropDownIcon />,
                                  },
                              ]
                            : []),
                        <SplitButton
                            variant="contained"
                            mainButton={mainButton}
                            options={options}
                            enableDropdownButton={enableDropdownButton}
                        />,
                    ]}
                />
                <VaporPage.Section>
                    <Filters
                        defaultQuery={defaultQuery}
                        query={query}
                        setQuery={setQuery}
                        archiveData={archiveData}
                        filterArchiveData={filterArchiveData}
                        onChangeFilterInputs={onChangeFilterInputs}
                        onChangeCheckbox={onChangeCheckbox}
                        onDateChange={onDateChange}
                        handleChangeMultiSelect={handleChangeMultiSelect}
                        selectedValues={selectedValues}
                        setSelectedValues={setSelectedValues}
                    />
                </VaporPage.Section>
                {!loading && <VaporPage.Section>{dataTable}</VaporPage.Section>}
                <ToastNotification
                    showNotification={showNotification}
                    setShowNotification={setShowNotification}
                    text={notificationText}
                    severity={notificationSeverity}
                />
            </VaporPage>
        </>
    );
};

const otherArchivePaths = Array.isArray(SIEBAR_PATHS)
    ? SIEBAR_PATHS.map((path) => ({
          target: "$ONE_LAYOUT_ROUTE",
          handler: {
              exact: true,
              path: path.url,
              element: <ArchiveSummary />,
          },
      }))
    : [];

export const archive = () => [
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/archive/archive",
            element: <Archive />,
        },
    },

    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/archive/summary",
            element: <ArchiveSummary />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,

            path: "/archive/summary/funds",
            element: <MergeList />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,

            path: "/archive/archive-profits-distribution",
            element: <DevisionedegliUtili />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/archivedeadlines/deadlines/macro",
            element: <MacroPage />,
        },
    },
    ...otherArchivePaths,
];
