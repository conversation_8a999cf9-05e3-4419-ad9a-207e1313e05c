export interface IQueryParams {
  page: number;
  pageSize: number;
  sortColumn: string;
  sortOrder: string;
  tipo: string;
}

export interface IDocumentFolders {
  t: any;
  folderTreeData: any;
  setFolderTreeData:React.Dispatch<React.SetStateAction<any>>;
}

export interface IContextMenu{
  mouseX: number;
  mouseY: number;
}

export interface IDefaultDeleteModal {
  open: boolean;
  title: string;
  confirmText: string;
  decline: string;
  agree: string;
  colorConfirmButton: string;
}
export interface IDefaultInfoErrorModal {
  open: boolean;
  confirmText: string;
}

