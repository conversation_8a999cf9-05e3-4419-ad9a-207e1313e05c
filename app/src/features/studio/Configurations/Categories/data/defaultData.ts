export const DEFAULT_QUERY = {
    page: 0,
    pageSize: 10,
    sortColumn: "nome",
    sortOrder: "asc",
    tipo: "",
};

export const DEFAULT_PARAMS = {
    nome: "",
    descrizione: "",
    campi: [],
    categoria_colonna: "",
};

export const INITIAL_TREEFOLDER_DATA = [
    {
        id: "root",
        text: "Pratic<PERSON>",
        icon: true,
        li_attr: { id: "root" },
        a_attr: { href: "#", id: "root_anchor" },
        state: {
            loaded: true,
            opened: true,
            selected: false,
            disabled: false,
        },
        parent: "#",
        type: "default",
    },
];

export const DEFAULT_DELETE_MODAL = {
    open: false,
    title: "Attenzione!",
    confirmText: "Sei sicuro di voler eliminare questa categoria?",
    decline: "Annulla",
    agree: "Conferma",
    colorConfirmButton: "error",
};

export const DEFAULT_INFO_ERROR_MODAL = {
    open: false,
    confirmText: "Il nome della categoria è già stato utilizzato",
};
