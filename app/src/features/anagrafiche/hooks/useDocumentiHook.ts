import useGetCustom from "../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { getDocumentiGrid } from "../helpers/documentiGridColumn";

interface QueryParams {
    [key: string]: any;
}

export const useDocumentiHook = (query: QueryParams) => {
    const { t } = useTranslation();
    const [columns, setColumns] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [list, setList] = useState<any>({
        totalRows: 0,
        currentPage: [],
    });
    const [previewOpen, setPreviewOpen] = useState(false);
    const [selectedDocumentId, setSelectedDocumentId] = useState(null);
    const [selectedFile, setSelectedFile] = useState("")

    const documentiRequest = useGetCustom("peopleuploadedfiles/list?noTemplateVars=true", query);
    const { handlePrintDocumenti } = usePrintDocumenti();

    const handlePreviewDocument = (id: any, nome: any) => {
        setSelectedDocumentId(id);
        setSelectedFile(nome)
        setPreviewOpen(true);
    };

    const initDocumenti = async () => {
        try {
            const finalColumns = await getDocumentiGrid(t, handlePrintDocumenti, handlePreviewDocument);
            setColumns(finalColumns);

            setLoading(true);
            const { data }: any = await documentiRequest.doFetch(true);
            setList({
                totalRows: data?.totalRows,
                currentPage: data?.currentPage,
            });
        } catch (error) {
            console.error("Error loading documenti grid:", error);
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        initDocumenti();
    }, [query]);

    return {
        initDocumenti,
        t,
        columns,
        list,
        loading,
        previewOpen,
        setPreviewOpen,
        selectedDocumentId,
        selectedFile,
    };
};

const usePrintDocumenti = () => {
    const printDocumenti = useGetCustom(`anagrafiche/get-file`, {}, null, true);

    const handlePrintDocumenti = async (id?: any, fileName?: any) => {
        const response: any = await printDocumenti.doFetch(true, { id });

        const fileExtension = fileName?.split('.').pop()?.toLowerCase() || '';

        const blob = new Blob([response.data], { type: `text/${fileExtension}` });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", `${fileName}`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    return { handlePrintDocumenti }
};  