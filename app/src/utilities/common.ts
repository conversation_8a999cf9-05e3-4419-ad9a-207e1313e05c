import { IGridColumn, IGridSettings } from "../interfaces/general.interfaces";

export const mapOtherList = (data: any): IGridSettings => {
    let { column_names, column_keys, sortable, column_widths } =
        data.gridsSettings;
    return {
        column_names: JSON.parse(JSON.stringify(column_names)),
        column_keys: JSON.parse(JSON.stringify(column_keys)),
        sortable: JSON.parse(JSON.stringify(sortable)),
        column_widths: JSON.parse(JSON.stringify(column_widths)),
    };
};

export const mapOtherColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = {};
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            returnColumn.renderCell = (params: any) => {
                const value = params.row[returnColumn.field];
                if (typeof value === "object" && value !== null) {
                    return value.label || "";
                }
                return value ?? "";
            };
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
