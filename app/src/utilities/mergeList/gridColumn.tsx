import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";

export const getMergeListArchiveGrid = async (t?: any) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Codice Archivio"),
                // t("Id"),
                // t("Protocollo generale"),
                t("Clienti"),
                t("Controparti"),
                t("Oggetto"),
                t("Stato"),
                // t("Action"),
            ],
            column_keys: [
                "codicearchivio",
                "listaclienti",
                "listacontroparti",
                "object",
                "status",
            ],
            column_widths: [
                "10%",
                "5%",
                "10%",
                "20%",
                "20%",
                "10%",
                "10%",
                "15%",
            ],
            sortable: [true, true, true, true, true, false, true, false],
            cell_templates: [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                "{{associa}}",
            ],
            header_templates: [null, null, null, null, null, null, null, null],
            column_totals: null,
        },
    });

    return mapMergeListColumnNames(response);
};

export const mapMergeListColumnNames = (response: any) => {
    const { column_names, column_keys, sortable }: IGridSettings =
        response.gridsSettings;

    let columns = column_names.map((cln: string, index: number) => {
        return {
            field: column_keys[index],
            headerName: cln,
            valueGetter: (row: any) => {
                return row["value"];
            },
            flex: 1,
            sortable: sortable[index],
        };
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
