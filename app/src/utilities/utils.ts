export const appendXDebugParamToUrl = (url: any) => {
    if (!import.meta.env.DEV) {
        return url;
    }

    if (!url.includes("XDEBUG_SESSION_START=PHPSTORM")) {
        const xDebugParam = "XDEBUG_SESSION_START=PHPSTORM";
        const separator = url.includes("?") ? "&" : "?";
        return `${url}${separator}${xDebugParam}`;
    }

    return url;
};

export const combineKeysAndValuesForDataTable = (keys: any, values: any) => {
    const result: any = [];
    keys.forEach((key: any, index: number) => {
        result.push({ id: key, label: values[index] });
    });

    return result;
};

export const joinWithDash = (
    strings: (string | null | undefined)[]
): string => {
    return strings
        .filter((s): s is string => !!s && s.trim() !== "")
        .join(" - ");
};

export const removeLinks = (input: string, replacement = "_") => {
    if (!input) {
        return "";
    }

    return input.replace(/<a[^>]*>.*?<\/a>/g, replacement);
};

const toFixedFix = (n: any, precisione: any) => {
    var k = Math.pow(10, precisione);
    return "" + Math.round(n * k) / k;
};

const number_format = (
    numero: any,
    decimali: any,
    dec_separatore: any,
    mig_separatore: any
) => {
    numero = (numero + "").replace(/[^0-9\.\-]?/gi, "");
    var n = 0;
    if (isFinite(+numero)) {
        n = numero;
    }
    var precisione = 0;
    if (isFinite(+decimali) && decimali > -1) {
        precisione = decimali;
    }
    var separatore = ".";
    if (typeof mig_separatore !== "undefined") {
        separatore = mig_separatore;
    }
    var dec: any = ",";
    if (typeof dec_separatore !== "undefined") {
        dec = dec_separatore;
    }
    var s: any = "";
    if (precisione !== 0) {
        s = toFixedFix(n, precisione);
    } else {
        s = "" + Math.round(n);
    }
    s = s.split(".");
    if (s[0].length > 3) {
        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, separatore);
    }
    if ((s[1] || "").length < precisione) {
        s[1] = s[1] || "";
        s[1] += new Array(precisione - s[1].length + 1).join("0");
    }
    return s.join(dec);
};

export { toFixedFix, number_format };

interface FileTypes {
    type: "pdf" | "csv" | "xlsx" | "txt" | "docx" | "log"
}

interface SaveFile {
    data: BlobPart | BlobPart[];
    fileName: string;
    setSaveFileError?: (error: boolean) => void;
}

type SaveFileOptions = SaveFile & FileTypes;

export function saveFile({
    data,
    fileName,
    type,
    setSaveFileError,
}: SaveFileOptions): void {
    try {
        const mimeType = {
            pdf: "application/pdf",
            csv: "text/csv",
            xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            txt: "text/plain",
            docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            log: "text/plain",
        }[type];

        const blob = new Blob(Array.isArray(data) ? data : [data], {
            type: mimeType,
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");

        const sanitizedFileName = fileName.endsWith(`.${type}`)
            ? fileName
            : `${fileName}.${type}`;

        link.href = url;
        link.download = sanitizedFileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        setSaveFileError?.(false);
    } catch (error) {
        setSaveFileError?.(true);
    }
}

export const formatWithDefault = (
    value: unknown,
    defaultValue: string = "-"
): string => {
    if (
        value === null ||
        value === undefined ||
        value === false ||
        value === ""
    ) {
        return defaultValue;
    }
    return String(value);
};
