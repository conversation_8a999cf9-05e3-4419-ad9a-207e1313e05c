import React, { useState, useCallback, useEffect } from "react";
import { DataGridPro } from "@vapor/v3-components";
import {
    GridColDef,
    GridPaginationModel,
    GridCallbackDetails,
    GridFeatureMode,
    GridSortModel,
    GridRowSelectionModel,
    GridToolbarContainer,
    GridToolbarColumnsButton,
    GridRowParams,
    useGridApiRef
} from "@mui/x-data-grid-pro";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSortUp, faSortDown } from "@fortawesome/free-solid-svg-icons";
import { useDataGridConfig } from "../services/DataGridConfigService";
import { sortColumnsByConfig, getColumnWidthsFromConfig, getVisibilityModelFromConfig } from "../helpers/dataGridColumnsHandler";

interface IDataGrid {
    data: any;
    columns: GridColDef[];
    pageSizeOptions?: number[];
    onPageChangeCallback?: (model: GridPaginationModel, details: GridCallbackDetails<any>) => void;
    name?: string;
    section?: string;
    paginationMode?: GridFeatureMode | undefined;
    totalRows: number;
    onClickKey?: string;
    onClickCheckboxKey?: string;
    sortingMode?: GridFeatureMode | undefined;
    page: number | undefined;
    pageSize: number;
    loading?: boolean;
    selectableRows?: boolean;
    onColumnOrderChange?: (allColumns: any) => void;
    onColumnWidthChange?: (allColumns: any) => void;
    onColumnVisibilityModelChange?: (allColumns: any) => void;
    onRowSelectionModelChange?: (rowSelectionModel: GridRowSelectionModel, details: GridCallbackDetails<any>) => void;
    onClickCallback?: (uniqueid: any) => void;
    query?: any;
    setQuery?: React.Dispatch<React.SetStateAction<any>>;
    hideFooterRowCount?: boolean;
    disableColumnResize?: boolean;
    disableColumnReorder?: boolean;
    disableColumnMenu?: boolean;
    hasAdditionaStyles?: boolean;
    isRowSelectableCustom?: any;
}

export const CustomDataGrid = (props: IDataGrid) => {
    const [config, setConfig] = useState<any>(null);

    const { columns, data, totalRows, selectableRows, loading, onClickKey = "uniqueid", page, pageSize, onClickCallback, query, setQuery, onColumnWidthChange, pageSizeOptions, onPageChangeCallback, paginationMode, sortingMode, onRowSelectionModelChange, onColumnOrderChange, onColumnVisibilityModelChange, hideFooterRowCount = false, disableColumnResize = false, disableColumnReorder = false, disableColumnMenu = false, name, isRowSelectableCustom, hasAdditionaStyles = true } = props;

    const apiRef = useGridApiRef();
    const { saveGridConfig, getGridConfig } = useDataGridConfig();

    const [columnVisibilityModel, setColumnVisibilityModel] = useState({});
    const defaultPageSizeOptions = [10, 20, 50];
    const defaultpaginationMode = "server";
    const defaultSortingMode = "server";

    useEffect(() => {
        const loadGridConfig = async () => {
            if (name) {
                try {
                    const config = await getGridConfig(name);

                    if (config) {
                        setConfig(config);
                    }

                    if (config?.columns_config) {
                        setColumnVisibilityModel(getVisibilityModelFromConfig(config));
                    }
                } catch (error) {
                    console.error("Error loading grid configuration:", error);
                }
            }
        };

        loadGridConfig();
    }, [name]);

    useEffect(() => {
        if (config?.columns_config && apiRef.current && columns.length > 0) {
            if (selectableRows) {
                config.columns_config.unshift({
                    field: "__check__",
                    width: 50,
                    hideable: true,
                    sortable: false,
                    headerName: "Checkbox selection"
                });
            }
            const configFields = config.columns_config.map((col: any) => col.field);
            setTimeout(() => {
                try {
                    const currentColumns = apiRef.current.getAllColumns();
                    const newColumnOrder = sortColumnsByConfig(currentColumns, configFields);

                    for (let i = 0; i < newColumnOrder.length; i++) {
                        apiRef.current.setColumnIndex(newColumnOrder[i], i);
                    }

                    const columnsToUpdate = getColumnWidthsFromConfig(config, currentColumns);
                    if (columnsToUpdate.length > 0) {
                        apiRef.current.updateColumns(columnsToUpdate);
                    }
                } catch (error) {
                    console.error("Error reordering columns:", error);
                }
            }, 100);
        }
    }, [config, apiRef, columns]);

    const onSortChange = useCallback(
        (sortModel: GridSortModel) => {
            if (setQuery !== undefined) {
                let newSortColumn = sortModel.length > 0 ? sortModel[0].field : query.sortColumn;
                let newSortOrder = sortModel.length > 0 ? sortModel[0].sort ?? "desc" : query.sortOrder;

                if (newSortColumn !== query.sortColumn || newSortOrder !== query.sortOrder) {
                    setQuery({
                        ...query,
                        sortColumn: newSortColumn,
                        sortOrder: newSortOrder
                    });
                }
            }
        },
        [setQuery, query]
    );

    const getRowIdCustom = (row: any) => {
        if (onClickKey === "custom") return Math.random().toString(36).slice(2, 7);
        return row[onClickKey];
    };

    const getColumnJson = (columns: any, optionColumn?: any) => {
        const columnJSon = columns.map(function (value: any) {
            let returnColumn: any = {
                field: value.field,
                headerName: value.headerName,
                flex: value.flex,
                sortable: value.sortable,
                hideable: value.hideable,
                width: value.width < 100 ? 100 : value.width,
                minWidth: 100
            };


            if (optionColumn) {
                returnColumn.hide = optionColumn[value.field] !== undefined && optionColumn[value.field] === false;
            }

            if (value.renderCell !== undefined) returnColumn.renderCell = value.renderCell;
            if (returnColumn.valueGetter !== undefined) returnColumn.valueGetter = value.valueGetter;
            return returnColumn;
        });
        return columnJSon;
    };

    const handleColumnOrderChange = async () => {
        const allColumn = apiRef.current.getAllColumns();
        const columnConfig = getColumnJson(allColumn);
        console.log(name);
        if (name) {
            try {
                await saveGridConfig({
                    columns_config: columnConfig,
                    name,
                    id: config?.id
                });
            } catch (error) {
                console.error("Error saving column order:", error);
            }
        }

        if (onColumnOrderChange !== undefined) {
            onColumnOrderChange(columnConfig);
        }
    };

    const handleColumnWidthChange = async () => {
        const allColumns = apiRef.current.getAllColumns();

        const adjustedColumns = allColumns.map((column: any) => {
            const width = column.width || 0;
            return {
                ...column,
                width: width < 100 ? 100 : width,
                minWidth: 100
            };
        });

        if (adjustedColumns.some((col: any, idx: number) => col.width !== allColumns[idx].width)) {
            apiRef.current.updateColumns(adjustedColumns);
        }

        const columnConfig = getColumnJson(adjustedColumns, columnVisibilityModel);

        if (name) {
            try {
                await saveGridConfig({
                    columns_config: columnConfig,
                    name,
                    id: config?.id
                });
            } catch (error) {
                console.error("Error saving column widths:", error);
            }
        }

        if (onColumnWidthChange !== undefined) {
            onColumnWidthChange(columnConfig);
        }
    };

    const handleColumnVisibilityModelChange = async (newModel: any) => {
        const allColumn = apiRef.current.getAllColumns();
        const columnConfig = getColumnJson(allColumn, newModel);

        setColumnVisibilityModel(newModel);

        if (name) {
            try {
                await saveGridConfig({
                    columns_config: columnConfig,
                    name,
                    id: config?.id
                });
            } catch (error) {
                console.error("Error saving column visibility:", error);
            }
        }

        if (onColumnVisibilityModelChange !== undefined) {
            onColumnVisibilityModelChange(columnConfig);
        }
    };

    const onClickCallbackCustom = (data: any) => {
        const uniqueid = onClickKey === "row" ? data["row"] : data["row"][onClickKey];
        if (onClickCallback !== undefined) onClickCallback(uniqueid);
    };

// const CustomToolbar = () => {
//     return (
//         <GridToolbarContainer>
//             <GridToolbarColumnsButton
//                 slotProps={{ button: { startIcon: <FontAwesomeIcon icon={faTableColumns} /> } }}
//             />
//         </GridToolbarContainer>
//     );
// };
    let defaultStyles =
        data?.length > 0
            ? { display: "grid", width: "100%" }
            : {
                display: "grid",
                width: "100%",
                minHeight: "230px",
                "&.MuiDataGrid-root .MuiDataGrid-virtualScroller": {
                    overflowY: "hidden"
                }
            };

    return (
        <DataGridPro
            apiRef={apiRef}
            sx={hasAdditionaStyles ? defaultStyles : {}}
            rows={data}
            columns={columns}
            pagination
            pageSizeOptions={pageSizeOptions !== undefined ? pageSizeOptions : defaultPageSizeOptions}
            paginationModel={{ page: page ?? 0, pageSize: pageSize }}
            onPaginationModelChange={onPageChangeCallback}
            paginationMode={paginationMode !== undefined ? paginationMode : defaultpaginationMode}
            rowCount={totalRows}
            getRowId={(row) => getRowIdCustom(row) || Math.random().toString(36).slice(2, 7)}
            sortingOrder={["asc", "desc"]}
            sortingMode={sortingMode !== undefined ? sortingMode : defaultSortingMode}
            sortModel={[
                {
                    field: query?.sortColumn,
                    sort: query?.sortOrder
                }
            ]}
            onSortModelChange={onSortChange}
            loading={loading}
            checkboxSelection={selectableRows}
            isRowSelectable={
                isRowSelectableCustom !== undefined
                    ? (params: GridRowParams) => isRowSelectableCustom(params)
                    : () => {
                        return true;
                    }
            }
            disableRowSelectionOnClick
            onRowSelectionModelChange={onRowSelectionModelChange}
            keepNonExistentRowsSelected
            onColumnOrderChange={handleColumnOrderChange}
            columnVisibilityModel={columnVisibilityModel}
            onColumnVisibilityModelChange={handleColumnVisibilityModelChange}
            onRowClick={onClickCallbackCustom}
            slots={{
                columnSortedAscendingIcon: () => <FontAwesomeIcon icon={faSortUp} />,
                columnSortedDescendingIcon: () => <FontAwesomeIcon icon={faSortDown} />,
                toolbar: () => (
                    <GridToolbarContainer>
                    <GridToolbarColumnsButton />
                    </GridToolbarContainer>
                )
            }}
            hideFooterRowCount={hideFooterRowCount}
            disableColumnResize={disableColumnResize}
            disableColumnReorder={disableColumnReorder}
            disableColumnMenu={disableColumnMenu}
            onColumnWidthChange={handleColumnWidthChange}
            disableColumnPinning
            disableColumnFilter
            disableColumnSelector={false}
            localeText={{
                noRowsLabel: "Nessun elemento",
                columnMenuSortAsc: "Ordine ascendente",
                columnMenuSortDesc: "Ordine discendente",
                columnMenuHideColumn: "Nascondi colonna",
                columnMenuShowColumns: "Mostra colonne",
                columnMenuManageColumns: "Gestisci colonne",

                footerRowSelected: (count) => `${count}  elementi selezionati`,
                footerTotalRows: "Elementi per pagina",
                footerTotalVisibleRows: (total: any, visible: any) => `${visible} di ${total}`,
                MuiTablePagination: {
                    labelRowsPerPage: "Elementi per pagina",
                    labelDisplayedRows: ({ from, to, count }) => `${from}-${to} di ${count}`
            },
            toolbarColumns: "Gestisci colonne",
            toolbarColumnsLabel: "Gestisci colonne",
            columnHeaderSortIconLabel: "Ordina"
        }}
        />
    );
};
