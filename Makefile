# Project Version
version?=0.0.8
help:
	@clear
	@echo ""
	@echo "---------------------"
	@echo "OneFront Boilerplate"
	@echo "Version: ${version}"
	@echo "---------------------"
	@echo ""
	@echo " 1) make boot ................. Starts the services & initializes the project"
	@echo " 2) make down ................. Stops the services and cleans up the local state"
	@echo " 3) make start ................ Starts the services"
	@echo " 4) make stop ................. Stops the services"
	@echo " 5) make boot-api ............. Starts the services without the frontend"
	@echo "                                and initializes the project"
	@echo " 6) make start-api ............ Starts the services without the frontend"
	@echo " 7) make init ................. Builds the initial state for development"
	@echo " 8) make clean ................ Removes any local state and dependencies"
	@echo " 9) make logs ................. Connects to the docker-compose logs"
	@echo ""
	@echo "    General Utilities"
	@echo "-----------------------------"
	@echo "00) make reset ................ Cleans & reboots the Project"
	@echo ""
	

# Whatever can be checked before starting the project
_check_boot:
	@if [ ! -f .env ]; then echo "\n\n====== BOOT ERROR ======\nLocal '.env' file not found;\nPlease create a '.env' file using the template from '.env.example'\n\n\n"; exit 1; fi

# Waiting for services to come to life
_check_healthz:
	@until curl -s http://localhost:4010/healthz > /dev/null; do sleep 1; done


# Start all the services binding ports on your machine
# > http://localhost:3000 - Start working on your App
start: _check_boot
	@echo "Starting the Project on Docker..."
	@docker-compose up -d
	@$(MAKE) -s -f Makefile _check_healthz
	@docker-compose exec -u root proxy sh -c 'echo "*********** test.netlex.cloud" >> /etc/hosts'
	@docker-compose exec -u root proxy sh -c 'echo "********** worker-test.netlex.cloud" >> /etc/hosts'

# Start the backend services binding ports on your machine
# You must run the client App manually
# (cd app && npm install && npm run start)
start-api: _check_boot
	@echo "Starting the Backend Services on Docker..."
	@docker-compose up -d proxy
	@$(MAKE) -s -f Makefile _check_healthz
	@docker-compose exec -u root proxy sh -c 'echo "*********** test.netlex.cloud" >> /etc/hosts'
	@docker-compose exec -u root proxy sh -c 'echo "********** worker-test.netlex.cloud" >> /etc/hosts'

# Restart the backend services
restart-api:
	@echo "Restarting the Backend Services on Docker..."
	@docker-compose restart proxy
	@$(MAKE) -s -f Makefile _check_healthz

stop:
	@echo "Stopping the Project..."
	@docker-compose down

# Applies any initial state to the project
# (migrations, configurations, ...)
# NOTE: this command is idempotent
init:
	@echo "Initializing the Project..."
	(npm install)

# Removes all the application's state
# think twice before running this, but run this as often as possible
# 🔥 like every morning or in between branch switching 🔥
clean:
	@echo "Deleting the Project's state..."
	@docker-compose down -v
	@echo "Deleting node_modules..."
	@rm -rf ./node_modules

# Forces a rebuild of any artifact
build:
	@echo "Pulling Docker images..."
	@docker-compose pull
	@echo "Building Docker images..."
	@docker-compose build --no-cache

logs:
	@docker-compose logs -f

restart: stop start
reset: stop build clean start-api
boot: start init logs
boot-api: start-api init logs
down: stop clean


#
# Frontend Only
#

_start-node:
	@if [ ! -d ./node_modules ]; then \
		echo "Installing node_modules..." ; \
		(npm install) ; \
	fi ; \
	echo "Starting the Frontend App on local NodeJS..." ; \
	(npm run dev) ; \

_check-npmrc-app:
	@echo "Checking for .npmrc file in the app directory..."
	@if [ -f .npmrc ]; then \
		echo "Found .npmrc file inside app directory" ; \
		$(MAKE) -s -f Makefile _start-node ; \
	else \
		echo "Please check your app directory for the missing .npmrc file..." ; \
	fi

_check-npmrc-user:
	@echo "Checking for .npmrc file in the user directory..."
	@if [ -f ~/.npmrc ]; then \
		echo "Found .npmrc file inside user directory" ; \
		$(MAKE) -s -f Makefile _start-node ; \
	else \
		echo "Please check your user directory for the missing .npmrc file, trying with app directory..." ; \
		$(MAKE) -s -f Makefile _check-npmrc-app ; \
	fi

start-app: _check-npmrc-user

clean-app:
	@echo "Deleting package-lock.json..."
	(rm -rf ./package-lock.json)
	@echo "Deleting local node_modules..."
	(rm -rf ./node_modules)

reset-app: clean-app start-app






#
# Numeric API
#

1: boot
2: down
3: start
4: stop
5: boot-api
6: start-api
7: init
8: clean
9: logs
00: reset
h: help